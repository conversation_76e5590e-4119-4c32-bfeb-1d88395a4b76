import { MongoClient, GridFSBucket } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DB_NAME = 'displaydb';

let client = null;
let db = null;
let bucket = null;

/**
 * Inicializa a conexão com o MongoDB
 */
export const connectToMongo = async () => {
  try {
    if (!client) {
      client = await MongoClient.connect(MONGODB_URI);
      db = client.db(DB_NAME);
      bucket = new GridFSBucket(db);
      console.log('Conectado ao MongoDB com sucesso');
    }
    return db;
  } catch (error) {
    console.error('Erro ao conectar ao MongoDB:', error);
    throw error;
  }
};

/**
 * Salva uma mídia no MongoDB usando GridFS
 * @param {Object} media - Objeto de mídia
 * @returns {Object} - Mídia salva com ID do MongoDB
 */
export const saveMedia = async (media) => {
  try {
    await connectToMongo();
    
    // Converter base64 para buffer se for uma imagem ou thumbnail
    let mediaBuffer = null;
    if (media.preview) {
      const base64Data = media.preview.split(',')[1];
      mediaBuffer = Buffer.from(base64Data, 'base64');
    }

    // Salvar arquivo no GridFS
    const uploadStream = bucket.openUploadStream(media.nome, {
      metadata: {
        type: media.type,
        lastModified: media.lastModified,
        size: media.size,
        originalUrl: media.originalUrl || ''
      }
    });

    if (mediaBuffer) {
      await new Promise((resolve, reject) => {
        uploadStream.end(mediaBuffer, (error) => {
          if (error) reject(error);
          else resolve();
        });
      });
    }

    // Salvar informações da mídia na coleção de mídias
    const mediaDoc = {
      _id: uploadStream.id,
      nome: media.nome,
      type: media.type,
      lastModified: media.lastModified,
      size: media.size,
      fileId: uploadStream.id,
      originalUrl: media.originalUrl || ''
    };

    await db.collection('midias').insertOne(mediaDoc);
    return { ...mediaDoc, id: mediaDoc._id };
  } catch (error) {
    console.error('Erro ao salvar mídia:', error);
    throw error;
  }
};

/**
 * Carrega todas as mídias do MongoDB
 * @returns {Array} - Array de mídias
 */
export const loadMedias = async () => {
  try {
    await connectToMongo();
    const midias = await db.collection('midias').find().toArray();
    return midias.map(midia => ({
      ...midia,
      id: midia._id,
      preview: `/api/media/${midia.fileId}` // URL para acessar o arquivo
    }));
  } catch (error) {
    console.error('Erro ao carregar mídias:', error);
    throw error;
  }
};

/**
 * Salva uma playlist no MongoDB
 * @param {Object} playlist - Objeto da playlist
 * @returns {Object} - Playlist salva com ID do MongoDB
 */
export const savePlaylist = async (playlist) => {
  try {
    await connectToMongo();
    const result = await db.collection('playlists').insertOne(playlist);
    return { ...playlist, id: result.insertedId };
  } catch (error) {
    console.error('Erro ao salvar playlist:', error);
    throw error;
  }
};

/**
 * Carrega todas as playlists do MongoDB
 * @returns {Array} - Array de playlists
 */
export const loadPlaylists = async () => {
  try {
    await connectToMongo();
    const playlists = await db.collection('playlists').find().toArray();
    return playlists.map(playlist => ({ ...playlist, id: playlist._id }));
  } catch (error) {
    console.error('Erro ao carregar playlists:', error);
    throw error;
  }
};

/**
 * Atualiza uma playlist no MongoDB
 * @param {string} id - ID da playlist
 * @param {Object} playlist - Dados atualizados da playlist
 * @returns {Object} - Playlist atualizada
 */
export const updatePlaylist = async (id, playlist) => {
  try {
    await connectToMongo();
    await db.collection('playlists').updateOne(
      { _id: id },
      { $set: playlist }
    );
    return { ...playlist, id };
  } catch (error) {
    console.error('Erro ao atualizar playlist:', error);
    throw error;
  }
};

/**
 * Remove uma mídia do MongoDB
 * @param {string} id - ID da mídia
 */
export const removeMedia = async (id) => {
  try {
    await connectToMongo();
    await bucket.delete(id);
    await db.collection('midias').deleteOne({ _id: id });
  } catch (error) {
    console.error('Erro ao remover mídia:', error);
    throw error;
  }
};

/**
 * Remove uma playlist do MongoDB
 * @param {string} id - ID da playlist
 */
export const removePlaylist = async (id) => {
  try {
    await connectToMongo();
    await db.collection('playlists').deleteOne({ _id: id });
  } catch (error) {
    console.error('Erro ao remover playlist:', error);
    throw error;
  }
};