/**
 * Utilitários para gerenciar o armazenamento de mídias e evitar exceder a quota do localStorage
 */

// Removendo limite de tamanho para permitir arquivos maiores
const MAX_STORAGE_SIZE = Number.MAX_SAFE_INTEGER; // Praticamente sem limite

/**
 * Comprime uma imagem para reduzir seu tamanho
 * @param {string} base64Image - Imagem em formato base64
 * @param {number} maxWidth - Largura máxima da imagem
 * @param {number} quality - Qualidade da imagem (0-1)
 * @returns {Promise<string>} - Imagem comprimida em formato base64
 */
export const compressImage = (base64Image, maxWidth = 1920, quality = 0.9) => {
  return new Promise((resolve, reject) => {
    try {
      const img = new Image();
      img.src = base64Image;

      img.onload = () => {
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;

        // Redimensionar mantendo a proporção
        if (width > maxWidth) {
          height = Math.round((height * maxWidth) / width);
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, width, height);

        // Converter para base64 com qualidade reduzida
        const compressedBase64 = canvas.toDataURL('image/jpeg', quality);
        resolve(compressedBase64);
      };

      img.onerror = (error) => {
        reject(error);
      };
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Gera uma miniatura para vídeos
 * @param {string} videoUrl - URL do vídeo
 * @returns {Promise<string>} - Miniatura em formato base64
 */
export const generateVideoThumbnail = (videoUrl) => {
  return new Promise((resolve, reject) => {
    try {
      const video = document.createElement('video');
      video.crossOrigin = 'anonymous';
      video.src = videoUrl;
      video.currentTime = 1; // Capturar frame após 1 segundo
      video.muted = true;

      video.onloadeddata = () => {
        try {
          const canvas = document.createElement('canvas');
          canvas.width = 320; // Largura da miniatura
          canvas.height = 180; // Altura da miniatura (proporção 16:9)

          const ctx = canvas.getContext('2d');
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          const thumbnailBase64 = canvas.toDataURL('image/jpeg', 0.7);
          resolve(thumbnailBase64);
        } catch (err) {
          reject(err);
        }
      };

      video.onerror = (error) => {
        reject(error);
      };

      // Timeout para evitar espera infinita
      setTimeout(() => {
        reject(new Error('Timeout ao gerar miniatura do vídeo'));
      }, 5000);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Otimiza uma mídia para armazenamento
 * @param {Object} mediaFile - Objeto de arquivo de mídia
 * @returns {Promise<Object>} - Objeto de mídia otimizado
 */
export const optimizeMediaForStorage = async (mediaFile) => {
  try {
    const isImage = mediaFile && mediaFile.type && mediaFile.type.startsWith('image/');
    const isVideo = mediaFile && mediaFile.type && mediaFile.type.startsWith('video/');

    let optimizedMedia = {
      id: mediaFile.id || Date.now(),
      nome: mediaFile.nome || mediaFile.file?.name || 'Mídia sem nome',
      type: mediaFile.type || mediaFile.file?.type,
      lastModified: mediaFile.lastModified || mediaFile.file?.lastModified || Date.now(),
      size: mediaFile.size || mediaFile.file?.size || 0,
    };

    // Para imagens, comprimir o preview
    if (isImage && mediaFile.preview) {
      optimizedMedia.preview = await compressImage(mediaFile.preview);
      optimizedMedia.originalUrl = mediaFile.preview; // Guardar referência original
    }
    // Para vídeos, NÃO converter para base64, manter arquivo original
    else if (isVideo) {
      // Para vídeos, manter o arquivo original sem conversão para base64
      optimizedMedia.arquivo = mediaFile.arquivo || mediaFile;
      optimizedMedia.preview = null; // Será definido pelo servidor após upload
      optimizedMedia.originalUrl = null; // Será definido pelo servidor após upload
      optimizedMedia.duration = mediaFile.duracao || 0;

      // Gerar thumbnail apenas se necessário (não para upload)
      if (mediaFile.preview && !mediaFile.arquivo) {
        try {
          optimizedMedia.thumbnail = await generateVideoThumbnail(mediaFile.preview);
        } catch (error) {
          console.warn('Erro ao gerar thumbnail do vídeo:', error);
        }
      }
    }
    // Caso não seja possível otimizar, manter o original
    else {
      optimizedMedia.preview = mediaFile.preview;
    }

    return optimizedMedia;
  } catch (error) {
    console.error('Erro ao otimizar mídia:', error);
    return mediaFile; // Retornar o original em caso de erro
  }
};

/**
 * Verifica o tamanho atual do localStorage
 * @returns {number} - Tamanho aproximado em bytes
 */
export const getStorageSize = () => {
  let totalSize = 0;
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      totalSize += (localStorage[key].length + key.length) * 2; // Aproximação para UTF-16
    }
  }
  return totalSize;
};

/**
 * Função modificada para não limitar o armazenamento
 * @param {number} requiredSpace - Espaço necessário em bytes
 * @returns {boolean} - Sempre retorna true para permitir o armazenamento
 */
export const cleanupStorageIfNeeded = (requiredSpace) => {
  return true; // Sempre permitir o armazenamento
};

/**
 * Salva mídias no MongoDB através do serviço HTTP
 * @param {Array} mediaItems - Array de mídias
 * @returns {Promise<boolean>} - Sucesso da operação
 */
export const saveMediaToStorage = async (mediaItems) => {
  try {
    const { httpService } = await import('../services/httpService');

    // Carregar mídias existentes do servidor
    let existingMedias = [];
    try {
      existingMedias = await httpService.loadMedias();
    } catch (error) {
      console.warn('Não foi possível carregar mídias existentes:', error);
      // Continuar mesmo sem conseguir carregar as mídias existentes
    }

    const existingIds = new Set(existingMedias.map(m => m.id || m._id));

    for (const media of mediaItems) {
      try {
        const mediaId = media.id || media._id;

        // Verificar se a mídia já existe no servidor
        if (mediaId && existingIds.has(mediaId)) {
          // Mídia já existe no servidor, pular
          continue;
        }

        // Verificar se a mídia tem arquivo válido antes de tentar salvar
        const arquivo = media.arquivo || media.file;
        const temArquivoValido = arquivo instanceof File ||
                               (media.preview && media.type) ||
                               (media.originalUrl && media.type);

        if (!temArquivoValido) {
          console.warn(`Mídia ${media.nome} não possui arquivo válido, pulando...`);
          continue;
        }

        // Nova mídia com arquivo válido, salvar no MongoDB
        await httpService.saveMedia(media);
      } catch (mediaError) {
        console.error('Erro ao salvar mídia individual:', mediaError);
        // Continuar com as próximas mídias mesmo se uma falhar
      }
    }

    return true;
  } catch (error) {
    console.error('Erro ao salvar mídias:', error);
    // Fallback para localStorage em caso de erro
    try {
      localStorage.setItem('midias', JSON.stringify(mediaItems));
      return true;
    } catch (e) {
      console.error('Erro ao salvar no localStorage:', e);
      return false;
    }
  }
};

/**
 * Salva playlists no MongoDB através do serviço HTTP
 * @param {Array} playlists - Array de playlists
 * @returns {Promise<boolean>} - Sucesso da operação
 */
export const savePlaylistsToStorage = async (playlists) => {
  try {
    const { httpService } = await import('../services/httpService');

    // Primeiro, carrega as playlists existentes do servidor
    const existingPlaylists = await httpService.loadPlaylists();
    const existingIds = new Set(existingPlaylists.map(p => p.id || p._id));

    for (const playlist of playlists) {
      const playlistId = playlist.id || playlist._id;

      if (playlistId && existingIds.has(playlistId)) {
        // Playlist existe, fazer update
        await httpService.updatePlaylist(playlistId, playlist);
      } else {
        // Playlist não existe, criar nova
        await httpService.savePlaylist(playlist);
      }
    }

    return true;
  } catch (error) {
    console.error('Erro ao salvar playlists:', error);
    return false;
  }
};

/**
 * Carrega mídias do MongoDB através do serviço HTTP
 * @returns {Promise<Array>} - Array de mídias
 */
export const loadMediaFromStorage = async () => {
  try {
    const { httpService } = await import('../services/httpService');
    const medias = await httpService.loadMedias();
    return medias || [];
  } catch (error) {
    console.error('Erro ao carregar mídias:', error);
    // Fallback para localStorage em caso de erro
    try {
      const savedMedia = localStorage.getItem('midias');
      return savedMedia ? JSON.parse(savedMedia) : [];
    } catch (e) {
      return [];
    }
  }
};

/**
 * Carrega playlists do MongoDB através do serviço HTTP
 * @returns {Promise<Array>} - Array de playlists
 */
export const loadPlaylistsFromStorage = async () => {
  try {
    const { httpService } = await import('../services/httpService');
    const playlists = await httpService.loadPlaylists();
    return playlists || [];
  } catch (error) {
    console.error('Erro ao carregar playlists:', error);
    // Fallback para localStorage em caso de erro
    try {
      const savedPlaylists = localStorage.getItem('playlists');
      return savedPlaylists ? JSON.parse(savedPlaylists) : [];
    } catch (e) {
      return [];
    }
  }
};