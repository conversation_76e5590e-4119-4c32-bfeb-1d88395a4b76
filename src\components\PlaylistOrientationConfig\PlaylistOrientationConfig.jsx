import React from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Paper,
  Grid,
  Chip,
  Alert,
  Switch,
  FormControlLabel
} from '@mui/material';
import PlaylistPlayIcon from '@mui/icons-material/PlaylistPlay';
import ScreenRotationIcon from '@mui/icons-material/ScreenRotation';
import { PLAYLIST_ORIENTATION_OPTIONS } from '../../constants/rotation';

const PlaylistOrientationConfig = ({
  playlistConfig = {},
  onConfigChange,
  disabled = false
}) => {

  const {
    orientation = 'mixed',
    autoRotate = false,
    transitionDuration = 500,
    adaptToContent = true,
    globalRotation = 0
  } = playlistConfig;

  const handleOrientationChange = (event) => {
    onConfigChange?.({
      ...playlistConfig,
      orientation: event.target.value
    });
  };

  const handleAutoRotateChange = (event) => {
    onConfigChange?.({
      ...playlistConfig,
      autoRotate: event.target.checked
    });
  };

  const handleTransitionDurationChange = (event) => {
    const newDuration = parseInt(event.target.value);
    console.log('🎛️ Mudança na duração da transição:', newDuration);
    onConfigChange?.({
      ...playlistConfig,
      transitionDuration: newDuration
    });
  };

  const handleAdaptToContentChange = (event) => {
    onConfigChange?.({
      ...playlistConfig,
      adaptToContent: event.target.checked
    });
  };

  const handleGlobalRotationChange = (event) => {
    onConfigChange?.({
      ...playlistConfig,
      globalRotation: parseInt(event.target.value)
    });
  };

  const transitionOptions = [
    { value: 0, label: 'Sem transição' },
    { value: 250, label: '0.25 segundos' },
    { value: 500, label: '0.5 segundos' },
    { value: 750, label: '0.75 segundos' },
    { value: 1000, label: '1 segundo' },
    { value: 1500, label: '1.5 segundos' },
    { value: 2000, label: '2 segundos' }
  ];

  const globalRotationOptions = [
    { value: 0, label: '0° (Normal)' },
    { value: 90, label: '90° (Horário)' },
    { value: 180, label: '180° (Invertido)' },
    { value: 270, label: '270° (Anti-horário)' }
  ];

  const getOrientationDescription = (orientation) => {
    const option = PLAYLIST_ORIENTATION_OPTIONS.find(opt => opt.value === orientation);
    return option ? option.description : '';
  };

  const getOrientationIcon = (orientation) => {
    switch (orientation) {
      case 'horizontal': return '📱';
      case 'vertical': return '📱';
      case 'mixed': return '🔄';
      default: return '📱';
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <PlaylistPlayIcon />
        Configurações de Orientação da Playlist
      </Typography>

      <Grid container spacing={2}>
        {/* Orientação Principal */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Orientação da Playlist</InputLabel>
            <Select
              value={orientation}
              onChange={handleOrientationChange}
              disabled={disabled}
              label="Orientação da Playlist"
            >
              {PLAYLIST_ORIENTATION_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span>{getOrientationIcon(option.value)}</span>
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {option.label}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {option.description}
                      </Typography>
                    </Box>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Rotação Global */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Rotação Global</InputLabel>
            <Select
              value={globalRotation}
              onChange={handleGlobalRotationChange}
              disabled={disabled}
              label="Rotação Global"
            >
              {globalRotationOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Configurações Avançadas */}
        <Grid item xs={12}>
          <Typography variant="subtitle2" gutterBottom sx={{ mt: 2, fontWeight: 'bold' }}>
            Configurações Avançadas
          </Typography>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={autoRotate}
                onChange={handleAutoRotateChange}
                disabled={disabled}
              />
            }
            label="Rotação Automática"
          />
          <Typography variant="caption" color="textSecondary" display="block">
            Rotaciona automaticamente baseado no conteúdo
          </Typography>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={adaptToContent}
                onChange={handleAdaptToContentChange}
                disabled={disabled}
              />
            }
            label="Adaptar ao Conteúdo"
          />
          <Typography variant="caption" color="textSecondary" display="block">
            Ajusta orientação baseada nas dimensões da mídia
          </Typography>
        </Grid>

        {/* Duração da Transição */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Duração da Transição</InputLabel>
            <Select
              value={transitionDuration}
              onChange={handleTransitionDurationChange}
              disabled={disabled}
              label="Duração da Transição"
            >
              {transitionOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Resumo das Configurações */}
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              Configuração Atual:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
              <Chip
                size="small"
                label={`${getOrientationIcon(orientation)} ${orientation.toUpperCase()}`}
                color="primary"
              />
              <Chip
                size="small"
                label={`Rotação: ${globalRotation}°`}
                color="secondary"
              />
              <Chip
                size="small"
                label={autoRotate ? 'Auto-rotação ON' : 'Auto-rotação OFF'}
                color={autoRotate ? 'success' : 'default'}
              />
              <Chip
                size="small"
                label={`Transição: ${transitionDuration}ms`}
                color="info"
              />
            </Box>
          </Alert>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default PlaylistOrientationConfig;
