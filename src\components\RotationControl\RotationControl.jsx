import React from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Typography,
  Paper,
  Grid,
  Tooltip,
  Chip
} from '@mui/material';
import RotateLeftIcon from '@mui/icons-material/RotateLeft';
import RotateRightIcon from '@mui/icons-material/RotateRight';
import ScreenRotationIcon from '@mui/icons-material/ScreenRotation';
import { ROTATION_OPTIONS, ORIENTATION_OPTIONS, getNextRotation, getPreviousRotation } from '../../constants/rotation';

const RotationControl = ({ 
  rotation = 0, 
  orientation = 'auto', 
  onRotationChange, 
  onOrientationChange,
  disabled = false,
  showPreview = true,
  compact = false 
}) => {
  
  const handleRotationChange = (event) => {
    const newRotation = parseInt(event.target.value);
    onRotationChange?.(newRotation);
  };

  const handleOrientationChange = (event) => {
    onOrientationChange?.(event.target.value);
  };

  const handleRotateLeft = () => {
    const newRotation = getPreviousRotation(rotation);
    onRotationChange?.(newRotation);
  };

  const handleRotateRight = () => {
    const newRotation = getNextRotation(rotation);
    onRotationChange?.(newRotation);
  };

  const getPreviewStyle = () => ({
    width: 60,
    height: 40,
    border: '2px solid #1976d2',
    borderRadius: '4px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    transform: `rotate(${rotation}deg)`,
    transition: 'transform 0.3s ease',
    fontSize: '12px',
    fontWeight: 'bold',
    color: '#1976d2'
  });

  if (compact) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Tooltip title="Rotacionar para esquerda">
          <IconButton 
            onClick={handleRotateLeft} 
            disabled={disabled}
            size="small"
          >
            <RotateLeftIcon />
          </IconButton>
        </Tooltip>
        
        <Chip 
          icon={<ScreenRotationIcon />}
          label={`${rotation}°`}
          variant="outlined"
          size="small"
        />
        
        <Tooltip title="Rotacionar para direita">
          <IconButton 
            onClick={handleRotateRight} 
            disabled={disabled}
            size="small"
          >
            <RotateRightIcon />
          </IconButton>
        </Tooltip>
      </Box>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <ScreenRotationIcon />
        Configurações de Rotação e Orientação
      </Typography>
      
      <Grid container spacing={2} alignItems="center">
        {/* Controles de Rotação */}
        <Grid item xs={12} md={6}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <Tooltip title="Rotacionar para esquerda (-90°)">
              <IconButton 
                onClick={handleRotateLeft} 
                disabled={disabled}
                color="primary"
              >
                <RotateLeftIcon />
              </IconButton>
            </Tooltip>
            
            <FormControl fullWidth size="small">
              <InputLabel>Rotação</InputLabel>
              <Select
                value={rotation}
                onChange={handleRotationChange}
                disabled={disabled}
                label="Rotação"
              >
                {ROTATION_OPTIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <span>{option.icon}</span>
                      <span>{option.label}</span>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <Tooltip title="Rotacionar para direita (+90°)">
              <IconButton 
                onClick={handleRotateRight} 
                disabled={disabled}
                color="primary"
              >
                <RotateRightIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Grid>

        {/* Controle de Orientação */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth size="small">
            <InputLabel>Orientação</InputLabel>
            <Select
              value={orientation}
              onChange={handleOrientationChange}
              disabled={disabled}
              label="Orientação"
            >
              {ORIENTATION_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span>{option.icon}</span>
                    <span>{option.label}</span>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Preview da Rotação */}
        {showPreview && (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 1 }}>
              <Typography variant="body2" color="textSecondary">
                Preview:
              </Typography>
              <Box sx={getPreviewStyle()}>
                TELA
              </Box>
              <Typography variant="body2" color="textSecondary">
                {rotation}° - {ORIENTATION_OPTIONS.find(o => o.value === orientation)?.label}
              </Typography>
            </Box>
          </Grid>
        )}
      </Grid>
    </Paper>
  );
};

export default RotationControl;
