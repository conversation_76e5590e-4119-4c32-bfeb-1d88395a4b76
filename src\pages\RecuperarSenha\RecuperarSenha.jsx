import React, { useState } from 'react';
import { Box, Container, TextField, Button, Typography, Alert } from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';

const validationSchema = yup.object({
  email: yup
    .string()
    .email('Digite um e-mail válido')
    .required('E-mail é obrigatório'),
});

const RecuperarSenha = () => {
  const [status, setStatus] = useState({ type: '', message: '' });

  const formik = useFormik({
    initialValues: {
      email: '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        // Implementar lógica de recuperação de senha aqui
        console.log('Recuperar senha para:', values.email);
        setStatus({
          type: 'success',
          message: 'Se o e-mail existir em nossa base, você receberá as instruções para redefinir sua senha.',
        });
      } catch (error) {
        setStatus({
          type: 'error',
          message: 'Ocorreu um erro ao processar sua solicitação. Tente novamente.',
        });
      }
    },
  });

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          bgcolor: 'white',
          p: 4,
          borderRadius: 2,
          boxShadow: '0 3px 5px 2px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Typography component="h1" variant="h5" sx={{ mb: 3, color: '#333' }}>
          Recuperar Senha
        </Typography>
        <Typography variant="body2" sx={{ mb: 3, textAlign: 'center', color: '#666' }}>
          Digite seu e-mail para receber as instruções de recuperação de senha
        </Typography>

        {status.type && (
          <Alert severity={status.type} sx={{ width: '100%', mb: 2 }}>
            {status.message}
          </Alert>
        )}

        <Box component="form" onSubmit={formik.handleSubmit} sx={{ mt: 1, width: '100%' }}>
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="E-mail"
            name="email"
            autoComplete="email"
            autoFocus
            value={formik.values.email}
            onChange={formik.handleChange}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            sx={{ mb: 3 }}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{
              mt: 3,
              mb: 2,
              bgcolor: '#007BFF',
              '&:hover': {
                bgcolor: '#0056b3',
              },
              height: '48px',
              fontSize: '1rem',
            }}
          >
            Enviar Instruções
          </Button>
          <Button
            fullWidth
            variant="text"
            href="/login"
            sx={{
              color: '#007BFF',
              textDecoration: 'none',
              '&:hover': {
                bgcolor: 'rgba(0, 123, 255, 0.1)',
              },
            }}
          >
            Voltar para o Login
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default RecuperarSenha;