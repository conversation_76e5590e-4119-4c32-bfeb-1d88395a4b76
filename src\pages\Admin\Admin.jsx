import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Alert,
  Chip,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  AdminPanelSettings as AdminIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import Navbar from '../../components/Navbar/Navbar';

const validationSchema = yup.object({
  email: yup
    .string()
    .email('Digite um e-mail válido')
    .required('E-mail é obrigatório'),
  password: yup
    .string()
    .min(6, 'A senha deve ter no mínimo 6 caracteres')
    .required('Senha é obrigatória'),
  name: yup
    .string()
    .required('Nome é obrigatório'),
});

const Admin = () => {
  const [users, setUsers] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [status, setStatus] = useState({ type: '', message: '' });

  // Carregar usuários do localStorage na inicialização
  useEffect(() => {
    const savedUsers = localStorage.getItem('users');
    if (savedUsers) {
      setUsers(JSON.parse(savedUsers));
    } else {
      // Usuários padrão
      const defaultUsers = [
        {
          id: 1,
          name: 'Sandro Denis',
          email: '<EMAIL>',
          password: 'Sandro2010',
          isAdmin: true,
          createdAt: new Date().toISOString()
        },
        {
          id: 2,
          name: 'Usuário Teste',
          email: '<EMAIL>',
          password: '123456',
          isAdmin: false,
          createdAt: new Date().toISOString()
        }
      ];
      setUsers(defaultUsers);
      localStorage.setItem('users', JSON.stringify(defaultUsers));
    }
  }, []);

  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      password: '',
      isAdmin: false,
    },
    validationSchema: validationSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        if (editingUser) {
          // Editar usuário existente
          const updatedUsers = users.map(user =>
            user.id === editingUser.id
              ? { ...user, ...values, updatedAt: new Date().toISOString() }
              : user
          );
          setUsers(updatedUsers);
          localStorage.setItem('users', JSON.stringify(updatedUsers));
          setStatus({
            type: 'success',
            message: 'Usuário atualizado com sucesso!'
          });
        } else {
          // Criar novo usuário
          const newUser = {
            id: Date.now(),
            ...values,
            createdAt: new Date().toISOString()
          };
          const updatedUsers = [...users, newUser];
          setUsers(updatedUsers);
          localStorage.setItem('users', JSON.stringify(updatedUsers));
          setStatus({
            type: 'success',
            message: 'Usuário criado com sucesso!'
          });
        }

        resetForm();
        setDialogOpen(false);
        setEditingUser(null);
      } catch (error) {
        setStatus({
          type: 'error',
          message: 'Erro ao salvar usuário. Tente novamente.'
        });
      }
    },
  });

  const handleEdit = (user) => {
    setEditingUser(user);
    formik.setValues({
      name: user.name,
      email: user.email,
      password: user.password,
      isAdmin: user.isAdmin
    });
    setDialogOpen(true);
  };

  const handleDelete = (userId) => {
    // Não permitir deletar o próprio usuário admin
    const currentUserEmail = localStorage.getItem('userEmail');
    const userToDelete = users.find(u => u.id === userId);

    if (userToDelete.email === currentUserEmail) {
      setStatus({
        type: 'error',
        message: 'Você não pode deletar sua própria conta!'
      });
      return;
    }

    const updatedUsers = users.filter(user => user.id !== userId);
    setUsers(updatedUsers);
    localStorage.setItem('users', JSON.stringify(updatedUsers));
    setStatus({
      type: 'success',
      message: 'Usuário removido com sucesso!'
    });
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingUser(null);
    formik.resetForm();
  };

  const handleAddUser = () => {
    setEditingUser(null);
    formik.resetForm();
    setDialogOpen(true);
  };

  return (
    <>
      <Navbar />
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ color: '#333', fontWeight: 'bold' }}>
          <AdminIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
          Painel de Administração
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Gerencie usuários do sistema
        </Typography>
      </Box>

      {status.type && (
        <Alert severity={status.type} sx={{ mb: 3 }} onClose={() => setStatus({ type: '', message: '' })}>
          {status.message}
        </Alert>
      )}

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total de Usuários
              </Typography>
              <Typography variant="h4">
                {users.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Administradores
              </Typography>
              <Typography variant="h4">
                {users.filter(u => u.isAdmin).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Usuários Comuns
              </Typography>
              <Typography variant="h4">
                {users.filter(u => !u.isAdmin).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" component="h2">
              Usuários do Sistema
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddUser}
              sx={{
                bgcolor: '#007BFF',
                '&:hover': { bgcolor: '#0056b3' }
              }}
            >
              Novo Usuário
            </Button>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Nome</TableCell>
                  <TableCell>E-mail</TableCell>
                  <TableCell>Tipo</TableCell>
                  <TableCell>Data de Criação</TableCell>
                  <TableCell align="center">Ações</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {user.isAdmin ? <AdminIcon sx={{ mr: 1, color: '#ff9800' }} /> : <PersonIcon sx={{ mr: 1, color: '#2196f3' }} />}
                        {user.name}
                      </Box>
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip
                        label={user.isAdmin ? 'Administrador' : 'Usuário'}
                        color={user.isAdmin ? 'warning' : 'primary'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {new Date(user.createdAt).toLocaleDateString('pt-BR')}
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        onClick={() => handleEdit(user)}
                        color="primary"
                        size="small"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        onClick={() => handleDelete(user.id)}
                        color="error"
                        size="small"
                        disabled={user.email === localStorage.getItem('userEmail')}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Dialog para criar/editar usuário */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingUser ? 'Editar Usuário' : 'Novo Usuário'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" onSubmit={formik.handleSubmit} sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="name"
              label="Nome"
              name="name"
              value={formik.values.name}
              onChange={formik.handleChange}
              error={formik.touched.name && Boolean(formik.errors.name)}
              helperText={formik.touched.name && formik.errors.name}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="E-mail"
              name="email"
              type="email"
              value={formik.values.email}
              onChange={formik.handleChange}
              error={formik.touched.email && Boolean(formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="password"
              label="Senha"
              name="password"
              type="password"
              value={formik.values.password}
              onChange={formik.handleChange}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
            />
            <Box sx={{ mt: 2 }}>
              <label>
                <input
                  type="checkbox"
                  name="isAdmin"
                  checked={formik.values.isAdmin}
                  onChange={formik.handleChange}
                  style={{ marginRight: 8 }}
                />
                Administrador
              </label>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancelar</Button>
          <Button
            onClick={formik.handleSubmit}
            variant="contained"
            sx={{
              bgcolor: '#007BFF',
              '&:hover': { bgcolor: '#0056b3' }
            }}
          >
            {editingUser ? 'Atualizar' : 'Criar'}
          </Button>
        </DialogActions>
      </Dialog>
      </Container>
    </>
  );
};

export default Admin;