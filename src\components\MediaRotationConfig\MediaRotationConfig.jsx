import React from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Paper,
  Grid,
  Chip,
  Divider
} from '@mui/material';
import RotationControl from '../RotationControl/RotationControl';
import { ROTATION_OPTIONS } from '../../constants/rotation';

const MediaRotationConfig = ({ 
  mediaConfig = {}, 
  onConfigChange,
  disabled = false 
}) => {
  
  const {
    rotation = 0,
    orientation = 'auto',
    fitMode = 'cover',
    duration = 10
  } = mediaConfig;

  const handleRotationChange = (newRotation) => {
    onConfigChange?.({
      ...mediaConfig,
      rotation: newRotation
    });
  };

  const handleOrientationChange = (newOrientation) => {
    onConfigChange?.({
      ...mediaConfig,
      orientation: newOrientation
    });
  };

  const handleFitModeChange = (event) => {
    onConfigChange?.({
      ...mediaConfig,
      fitMode: event.target.value
    });
  };

  const handleDurationChange = (event) => {
    onConfigChange?.({
      ...mediaConfig,
      duration: parseInt(event.target.value)
    });
  };

  const fitModeOptions = [
    { value: 'cover', label: 'Cobrir (Cover)', description: 'Preenche toda a tela, pode cortar' },
    { value: 'contain', label: 'Conter (Contain)', description: 'Mostra toda a mídia, pode ter bordas' },
    { value: 'fill', label: 'Preencher (Fill)', description: 'Estica para preencher, pode distorcer' },
    { value: 'scale-down', label: 'Reduzir (Scale-down)', description: 'Reduz se necessário' }
  ];

  const durationOptions = [
    { value: 5, label: '5 segundos' },
    { value: 10, label: '10 segundos' },
    { value: 15, label: '15 segundos' },
    { value: 20, label: '20 segundos' },
    { value: 30, label: '30 segundos' },
    { value: 60, label: '1 minuto' },
    { value: 120, label: '2 minutos' },
    { value: 300, label: '5 minutos' }
  ];

  return (
    <Paper elevation={1} sx={{ p: 2, mb: 2, backgroundColor: '#fafafa' }}>
      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
        ⚙️ Configurações da Mídia
      </Typography>
      
      <Grid container spacing={2}>
        {/* Rotação e Orientação */}
        <Grid item xs={12}>
          <RotationControl
            rotation={rotation}
            orientation={orientation}
            onRotationChange={handleRotationChange}
            onOrientationChange={handleOrientationChange}
            disabled={disabled}
            showPreview={true}
            compact={false}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 1 }} />
        </Grid>

        {/* Modo de Ajuste */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth size="small">
            <InputLabel>Modo de Ajuste</InputLabel>
            <Select
              value={fitMode}
              onChange={handleFitModeChange}
              disabled={disabled}
              label="Modo de Ajuste"
            >
              {fitModeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {option.label}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {option.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Duração (apenas para imagens) */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth size="small">
            <InputLabel>Duração de Exibição</InputLabel>
            <Select
              value={duration}
              onChange={handleDurationChange}
              disabled={disabled}
              label="Duração de Exibição"
            >
              {durationOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Resumo das Configurações */}
        <Grid item xs={12}>
          <Box sx={{ mt: 1 }}>
            <Typography variant="caption" color="textSecondary" gutterBottom>
              Configuração Atual:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip 
                size="small" 
                label={`Rotação: ${rotation}°`} 
                color="primary" 
                variant="outlined" 
              />
              <Chip 
                size="small" 
                label={`Orientação: ${orientation}`} 
                color="secondary" 
                variant="outlined" 
              />
              <Chip 
                size="small" 
                label={`Ajuste: ${fitMode}`} 
                color="info" 
                variant="outlined" 
              />
              <Chip 
                size="small" 
                label={`Duração: ${duration}s`} 
                color="success" 
                variant="outlined" 
              />
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default MediaRotationConfig;
