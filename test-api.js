import https from 'https';
import http from 'http';

async function testAPI() {
  try {
    console.log('🧪 Testando API de mídias...');

    const response = await new Promise((resolve, reject) => {
      const req = http.get('http://localhost:3001/api/medias', (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (e) {
            reject(e);
          }
        });
      });
      req.on('error', reject);
    });

    console.log(`📊 Encontradas ${response.length} mídias:`);

    response.forEach((media, index) => {
      console.log(`\n${index + 1}. ${media.nome}`);
      console.log(`   Tipo: ${media.type}`);
      console.log(`   Preview: ${media.preview ? media.preview.substring(0, 50) + '...' : 'N/A'}`);
      console.log(`   OriginalUrl: ${media.originalUrl ? media.originalUrl.substring(0, 50) + '...' : 'N/A'}`);

      // Verificar se as URLs são absolutas
      if (media.preview && media.preview.startsWith('http://localhost:3001')) {
        console.log('   ✅ URL absoluta correta!');
      } else if (media.preview && media.preview.startsWith('data:')) {
        console.log('   ✅ Base64 (imagem)');
      } else {
        console.log('   ❌ URL incorreta!');
      }
    });

  } catch (error) {
    console.error('❌ Erro ao testar API:', error);
  }
}

testAPI();
