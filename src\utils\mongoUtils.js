import { connectToMongo, saveMedia, loadMedias, savePlaylist, loadPlaylists, updatePlaylist, removeMedia, removePlaylist } from '../services/mongoService';

/**
 * Salva uma mídia no MongoDB
 * @param {Object} media - Objeto de mídia para salvar
 * @returns {Promise<Object>} - Mídia salva com ID
 */
export const saveMediaToMongo = async (media) => {
  try {
    const savedMedia = await saveMedia(media);
    return savedMedia;
  } catch (error) {
    console.error('Erro ao salvar mídia no MongoDB:', error);
    throw error;
  }
};

/**
 * Carrega todas as mídias do MongoDB
 * @returns {Promise<Array>} - Array de mídias
 */
export const loadMediaFromMongo = async () => {
  try {
    const medias = await loadMedias();
    return medias;
  } catch (error) {
    console.error('Erro ao carregar mídias do MongoDB:', error);
    throw error;
  }
};

/**
 * Salva uma playlist no MongoDB
 * @param {Object} playlist - Objeto da playlist
 * @returns {Promise<Object>} - Playlist salva com ID
 */
export const savePlaylistToMongo = async (playlist) => {
  try {
    const savedPlaylist = await savePlaylist(playlist);
    return savedPlaylist;
  } catch (error) {
    console.error('Erro ao salvar playlist no MongoDB:', error);
    throw error;
  }
};

/**
 * Carrega todas as playlists do MongoDB
 * @returns {Promise<Array>} - Array de playlists
 */
export const loadPlaylistsFromMongo = async () => {
  try {
    const playlists = await loadPlaylists();
    return playlists;
  } catch (error) {
    console.error('Erro ao carregar playlists do MongoDB:', error);
    throw error;
  }
};

/**
 * Atualiza uma playlist no MongoDB
 * @param {string} id - ID da playlist
 * @param {Object} playlist - Dados atualizados da playlist
 * @returns {Promise<Object>} - Playlist atualizada
 */
export const updatePlaylistInMongo = async (id, playlist) => {
  try {
    const updatedPlaylist = await updatePlaylist(id, playlist);
    return updatedPlaylist;
  } catch (error) {
    console.error('Erro ao atualizar playlist no MongoDB:', error);
    throw error;
  }
};

/**
 * Remove uma mídia do MongoDB
 * @param {string} id - ID da mídia
 */
export const removeMediaFromMongo = async (id) => {
  try {
    await removeMedia(id);
  } catch (error) {
    console.error('Erro ao remover mídia do MongoDB:', error);
    throw error;
  }
};

/**
 * Remove uma playlist do MongoDB
 * @param {string} id - ID da playlist
 */
export const removePlaylistFromMongo = async (id) => {
  try {
    await removePlaylist(id);
  } catch (error) {
    console.error('Erro ao remover playlist do MongoDB:', error);
    throw error;
  }
};