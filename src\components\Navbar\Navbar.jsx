import React from 'react';
import { App<PERSON><PERSON>, Toolbar, Button, Box, IconButton, Typography, useTheme, Avatar } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import LogoutIcon from '@mui/icons-material/Logout';
import DashboardIcon from '@mui/icons-material/Dashboard';
import TvIcon from '@mui/icons-material/Tv';
import MovieIcon from '@mui/icons-material/Movie';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isAdmin = localStorage.getItem('isAdmin') === 'true';
  const userName = localStorage.getItem('userName') || 'Usuário';
  const userEmail = localStorage.getItem('userEmail') || '';

  const isActive = (path) => location.pathname === path;

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userName');
    localStorage.removeItem('isAdmin');
    navigate('/login');
  };

  return (
    <AppBar
      position="static"
      sx={{
        background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
        boxShadow: '0 3px 5px 2px rgba(0, 0, 0, 0.15)'
      }}
    >
      <Toolbar sx={{ display: 'flex', justifyContent: 'space-between', py: 1 }}>
        {/* Logo/Brand e Menu de Navegação */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Logo/Brand */}
          <Typography
            variant="h6"
            sx={{
              fontWeight: 'bold',
              color: 'white',
              mr: { xs: 1, md: 2 },
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              fontSize: { xs: '1rem', md: '1.25rem' }
            }}
          >
            <span style={{ fontSize: '1.2em' }}>📺</span>
            <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>
              Digital Signage
            </Box>
            <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' } }}>
              DS
            </Box>
          </Typography>

          {/* Menu de Navegação */}
          <Box sx={{ display: 'flex', gap: { xs: 0.5, md: 1 } }}>
            <Button
              color="inherit"
              startIcon={<DashboardIcon />}
              onClick={() => navigate('/dashboard')}
              sx={{
                bgcolor: isActive('/dashboard') ? 'rgba(255, 255, 255, 0.25)' : 'transparent',
                borderRadius: '10px',
                px: { xs: 1.5, md: 2.5 },
                py: 1,
                fontSize: { xs: '12px', md: '14px' },
                fontWeight: isActive('/dashboard') ? 'bold' : 'medium',
                border: isActive('/dashboard') ? '1px solid rgba(255, 255, 255, 0.4)' : '1px solid transparent',
                minWidth: { xs: 'auto', md: 'auto' },
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                },
                transition: 'all 0.3s ease',
              }}
            >
              <Box component="span" sx={{ display: { xs: 'none', lg: 'inline' } }}>
                Dashboard
              </Box>
              <Box component="span" sx={{ display: { xs: 'inline', lg: 'none' } }}>
                Home
              </Box>
            </Button>
            <Button
              color="inherit"
              startIcon={<TvIcon />}
              onClick={() => navigate('/gerenciamento-telas')}
              sx={{
                bgcolor: isActive('/gerenciamento-telas') ? 'rgba(255, 255, 255, 0.25)' : 'transparent',
                borderRadius: '10px',
                px: { xs: 1.5, md: 2.5 },
                py: 1,
                fontSize: { xs: '12px', md: '14px' },
                fontWeight: isActive('/gerenciamento-telas') ? 'bold' : 'medium',
                border: isActive('/gerenciamento-telas') ? '1px solid rgba(255, 255, 255, 0.4)' : '1px solid transparent',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                },
                transition: 'all 0.3s ease',
              }}
            >
              Telas
            </Button>
            <Button
              color="inherit"
              startIcon={<MovieIcon />}
              onClick={() => navigate('/gerenciamento-midias')}
              sx={{
                bgcolor: isActive('/gerenciamento-midias') ? 'rgba(255, 255, 255, 0.25)' : 'transparent',
                borderRadius: '10px',
                px: { xs: 1.5, md: 2.5 },
                py: 1,
                fontSize: { xs: '12px', md: '14px' },
                fontWeight: isActive('/gerenciamento-midias') ? 'bold' : 'medium',
                border: isActive('/gerenciamento-midias') ? '1px solid rgba(255, 255, 255, 0.4)' : '1px solid transparent',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                },
                transition: 'all 0.3s ease',
              }}
            >
              Mídias
            </Button>
            {isAdmin && (
              <Button
                color="inherit"
                startIcon={<AdminPanelSettingsIcon />}
                onClick={() => navigate('/admin')}
                sx={{
                  bgcolor: isActive('/admin') ? 'rgba(255, 152, 0, 0.3)' : 'rgba(255, 152, 0, 0.1)',
                  borderRadius: '10px',
                  px: { xs: 1.5, md: 2.5 },
                  py: 1,
                  fontSize: { xs: '12px', md: '14px' },
                  fontWeight: isActive('/admin') ? 'bold' : 'medium',
                  border: '1px solid rgba(255, 152, 0, 0.4)',
                  '&:hover': {
                    bgcolor: 'rgba(255, 152, 0, 0.25)',
                    transform: 'translateY(-1px)',
                    boxShadow: '0 2px 8px rgba(255, 152, 0, 0.2)'
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Admin
              </Button>
            )}
          </Box>
        </Box>

        {/* Área do Usuário */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Informações do Usuário */}
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1.5,
            px: 2,
            py: 1,
            bgcolor: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '12px',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            backdropFilter: 'blur(10px)',
            transition: 'all 0.3s ease',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.15)',
              transform: 'translateY(-1px)',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
            }
          }}>
            {/* Avatar do Usuário */}
            <Avatar
              sx={{
                width: 36,
                height: 36,
                bgcolor: isAdmin ? '#ff9800' : '#2196f3',
                color: 'white',
                fontSize: '16px',
                fontWeight: 'bold',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
                border: '2px solid rgba(255, 255, 255, 0.3)'
              }}
            >
              {userName.charAt(0).toUpperCase()}
            </Avatar>

            {/* Informações do Usuário */}
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
              <Typography
                variant="body2"
                sx={{
                  fontWeight: 'bold',
                  lineHeight: 1.2,
                  color: 'white',
                  fontSize: '14px'
                }}
              >
                {userName}
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  opacity: 0.9,
                  lineHeight: 1,
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5
                }}
              >
                {isAdmin ? '👑 Admin' : '👤 Usuário'} • {userEmail.split('@')[0]}
              </Typography>
            </Box>
          </Box>

          {/* Botão de Logout */}
          <IconButton
            color="inherit"
            onClick={handleLogout}
            sx={{
              bgcolor: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '12px',
              width: 44,
              height: 44,
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                transform: 'scale(1.05)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
              },
              transition: 'all 0.3s ease',
            }}
          >
            <LogoutIcon sx={{ fontSize: '20px' }} />
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;