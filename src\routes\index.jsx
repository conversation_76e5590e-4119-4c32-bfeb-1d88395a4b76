import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Login from '../pages/Login/Login';
import RecuperarSenha from '../pages/RecuperarSenha/RecuperarSenha';
import CadastroEmpresa from '../pages/CadastroEmpresa/CadastroEmpresa';
import Dashboard from '../pages/Dashboard/Dashboard';
import GerenciamentoTelas from '../pages/GerenciamentoTelas/GerenciamentoTelas';
import GerenciamentoMidias from '../pages/GerenciamentoMidias/GerenciamentoMidias';
import Display from '../pages/Display/Display';
import Test from '../pages/Test/Test';
import Admin from '../pages/Admin/Admin';

const PrivateRoute = ({ children }) => {
  // Temporariamente desabilitando autenticação para debug
  return children;
  // const isAuthenticated = localStorage.getItem('token'); // Implementar lógica de autenticação adequada
  // return isAuthenticated ? children : <Navigate to="/login" />;
};

const AdminRoute = ({ children }) => {
  const isAuthenticated = localStorage.getItem('token');
  const isAdmin = localStorage.getItem('isAdmin') === 'true';

  // Temporariamente desabilitando verificação de autenticação para debug
  // if (!isAuthenticated) {
  //   return <Navigate to="/login" />;
  // }

  if (!isAdmin) {
    return <Navigate to="/dashboard" />;
  }

  return children;
};

const Router = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/recuperar-senha" element={<RecuperarSenha />} />
        <Route path="/cadastro" element={<CadastroEmpresa />} />
        <Route
          path="/dashboard"
          element={
            <PrivateRoute>
              <Dashboard />
            </PrivateRoute>
          }
        />
        <Route
          path="/gerenciamento-telas"
          element={
            <PrivateRoute>
              <GerenciamentoTelas />
            </PrivateRoute>
          }
        />
        <Route
          path="/gerenciamento-midias"
          element={
            <PrivateRoute>
              <GerenciamentoMidias />
            </PrivateRoute>
          }
        />
        <Route
          path="/admin"
          element={
            <AdminRoute>
              <Admin />
            </AdminRoute>
          }
        />
        <Route path="/" element={<Navigate to="/dashboard" />} />
        <Route path="/display/:id" element={<Display />} />
        <Route path="/test" element={<Test />} />
      </Routes>
    </BrowserRouter>
  );
};

export default Router;