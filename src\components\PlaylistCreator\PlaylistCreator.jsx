import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>,
  Typo<PERSON>,
  Stepper,
  Step,
  StepLabel,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  Chip,
  Button,
  TextField,
  Grid,
  Paper,
  Fade,
  Zoom,
  useTheme,
  alpha
} from '@mui/material';
import {
  PlaylistAdd as PlaylistAddIcon,
  VideoLibrary as VideoIcon,
  Image as ImageIcon,
  DragIndicator as DragIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
  Preview as PreviewIcon,
  Save as SaveIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import PlaylistOrientationConfig from '../PlaylistOrientationConfig/PlaylistOrientationConfig';

const PlaylistCreator = ({
  open,
  onClose,
  onSave,
  selectedMidias = [],
  onMidiasChange,
  availableMidias = [],
  playlistData = null,
  playlistConfig = {},
  onConfigChange
}) => {
  const theme = useTheme();
  const [activeStep, setActiveStep] = useState(0);
  const [playlistInfo, setPlaylistInfo] = useState({
    nome: playlistData?.nome || '',
    descricao: playlistData?.descricao || ''
  });

  // Detectar se está editando uma playlist existente
  const isEditing = !!playlistData;

  // Atualizar dados quando playlistData mudar (para edição)
  React.useEffect(() => {
    if (playlistData) {
      setPlaylistInfo({
        nome: playlistData.nome || '',
        descricao: playlistData.descricao || ''
      });
    } else {
      setPlaylistInfo({
        nome: '',
        descricao: ''
      });
    }
  }, [playlistData]);

  const steps = [
    { label: 'Informações', icon: <PlaylistAddIcon /> },
    { label: 'Selecionar Mídias', icon: <VideoIcon /> },
    { label: 'Organizar', icon: <DragIcon /> },
    { label: 'Configurações', icon: <SettingsIcon /> },
    { label: 'Finalizar', icon: <SaveIcon /> }
  ];

  const handleNext = () => {
    setActiveStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const handleBack = () => {
    setActiveStep((prev) => Math.max(prev - 1, 0));
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(selectedMidias);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    onMidiasChange(items.map((midia, index) => ({
      ...midia,
      ordem: index + 1
    })));
  };

  const addMidiaToPlaylist = (midia) => {
    if (selectedMidias.some(m => m.id === midia.id)) return;

    const newMidia = {
      ...midia,
      ordem: selectedMidias.length + 1,
      duracao: midia.type?.startsWith('video/') ? 30 : 10
    };

    onMidiasChange([...selectedMidias, newMidia]);
  };

  const removeMidiaFromPlaylist = (midiaId) => {
    const updatedMidias = selectedMidias.filter(m => m.id !== midiaId);
    onMidiasChange(updatedMidias.map((midia, index) => ({
      ...midia,
      ordem: index + 1
    })));
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{
              background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold',
              mb: 3
            }}>
              📝 Informações da Playlist
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nome da Playlist"
                  value={playlistInfo.nome}
                  onChange={(e) => setPlaylistInfo(prev => ({ ...prev, nome: e.target.value }))}
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '12px',
                      '&:hover fieldset': {
                        borderColor: '#007BFF',
                      },
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Descrição (opcional)"
                  value={playlistInfo.descricao}
                  onChange={(e) => setPlaylistInfo(prev => ({ ...prev, descricao: e.target.value }))}
                  variant="outlined"
                  multiline
                  rows={3}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '12px',
                      '&:hover fieldset': {
                        borderColor: '#007BFF',
                      },
                    },
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        );

      case 1:
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{
              background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold',
              mb: 3
            }}>
              🎬 Selecionar Mídias
            </Typography>

            <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
              <Chip
                label={`${selectedMidias.length} selecionadas`}
                color="primary"
                variant="filled"
              />
              <Chip
                label={`${availableMidias.length} disponíveis`}
                color="secondary"
                variant="outlined"
              />
            </Box>

            <Grid container spacing={2} sx={{ maxHeight: '400px', overflow: 'auto' }}>
              {availableMidias.map((midia) => {
                const isSelected = selectedMidias.some(m => m.id === midia.id);
                return (
                  <Grid item xs={12} sm={6} md={4} key={midia.id}>
                    <Zoom in timeout={300}>
                      <Card
                        sx={{
                          position: 'relative',
                          cursor: 'pointer',
                          transition: 'all 0.3s ease',
                          transform: isSelected ? 'scale(0.95)' : 'scale(1)',
                          opacity: isSelected ? 0.7 : 1,
                          border: isSelected ? '2px solid #007BFF' : '1px solid transparent',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: '0 8px 25px rgba(0,123,255,0.3)'
                          }
                        }}
                        onClick={() => addMidiaToPlaylist(midia)}
                      >
                        {midia.type?.startsWith('image/') ? (
                          <CardMedia
                            component="img"
                            height="120"
                            image={midia.preview || midia.originalUrl}
                            alt={midia.nome}
                          />
                        ) : (
                          <Box sx={{
                            height: 120,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                          }}>
                            <VideoIcon sx={{ fontSize: 40, color: 'white' }} />
                          </Box>
                        )}

                        <CardContent sx={{ p: 2 }}>
                          <Typography variant="body2" fontWeight="bold" noWrap>
                            {midia.nome}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                            {midia.type?.startsWith('video/') ? (
                              <Chip label="Vídeo" size="small" color="primary" />
                            ) : (
                              <Chip label="Imagem" size="small" color="secondary" />
                            )}
                          </Box>
                        </CardContent>

                        {isSelected && (
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            background: '#007BFF',
                            borderRadius: '50%',
                            width: 24,
                            height: 24,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <Typography variant="caption" color="white" fontWeight="bold">
                              ✓
                            </Typography>
                          </Box>
                        )}
                      </Card>
                    </Zoom>
                  </Grid>
                );
              })}
            </Grid>
          </Box>
        );

      case 2:
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{
              background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold',
              mb: 3
            }}>
              🎯 Organizar Mídias
            </Typography>

            {selectedMidias.length === 0 ? (
              <Paper sx={{
                p: 4,
                textAlign: 'center',
                background: 'linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,196,159,0.1) 100%)',
                border: '2px dashed rgba(0,123,255,0.3)',
                borderRadius: '12px'
              }}>
                <VideoIcon sx={{ fontSize: 60, color: 'rgba(0,123,255,0.5)', mb: 2 }} />
                <Typography variant="h6" color="textSecondary">
                  Nenhuma mídia selecionada
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Volte ao step anterior para selecionar mídias
                </Typography>
              </Paper>
            ) : (
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="playlist">
                  {(provided) => (
                    <Box
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      sx={{ maxHeight: '400px', overflow: 'auto' }}
                    >
                      {selectedMidias.map((midia, index) => (
                        <Draggable key={midia.id} draggableId={midia.id.toString()} index={index}>
                          {(provided, snapshot) => (
                            <Card
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              sx={{
                                mb: 2,
                                transform: snapshot.isDragging ? 'rotate(5deg)' : 'none',
                                boxShadow: snapshot.isDragging ? '0 8px 25px rgba(0,0,0,0.3)' : '0 2px 8px rgba(0,0,0,0.1)',
                                transition: 'all 0.2s ease',
                                background: snapshot.isDragging ? 'linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,196,159,0.1) 100%)' : 'white'
                              }}
                            >
                              <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                                <Box {...provided.dragHandleProps}>
                                  <DragIcon sx={{ color: 'rgba(0,0,0,0.5)', cursor: 'grab' }} />
                                </Box>

                                <Chip
                                  label={index + 1}
                                  size="small"
                                  color="primary"
                                  sx={{ minWidth: 32 }}
                                />

                                {midia.type?.startsWith('image/') ? (
                                  <Box sx={{
                                    width: 60,
                                    height: 40,
                                    borderRadius: '8px',
                                    overflow: 'hidden',
                                    flexShrink: 0
                                  }}>
                                    <img
                                      src={midia.preview || midia.originalUrl}
                                      alt={midia.nome}
                                      style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                    />
                                  </Box>
                                ) : (
                                  <Box sx={{
                                    width: 60,
                                    height: 40,
                                    borderRadius: '8px',
                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexShrink: 0
                                  }}>
                                    <VideoIcon sx={{ color: 'white', fontSize: 20 }} />
                                  </Box>
                                )}

                                <Box sx={{ flexGrow: 1 }}>
                                  <Typography variant="body1" fontWeight="bold">
                                    {midia.nome}
                                  </Typography>
                                  <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                                    {midia.type?.startsWith('video/') ? (
                                      <Chip label="Vídeo" size="small" color="primary" />
                                    ) : (
                                      <Chip label="Imagem" size="small" color="secondary" />
                                    )}
                                    <Chip
                                      label={`${midia.duracao || 10}s`}
                                      size="small"
                                      variant="outlined"
                                    />
                                  </Box>
                                </Box>

                                <IconButton
                                  onClick={() => removeMidiaFromPlaylist(midia.id)}
                                  color="error"
                                  size="small"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </CardContent>
                            </Card>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </Box>
                  )}
                </Droppable>
              </DragDropContext>
            )}
          </Box>
        );

      case 3:
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{
              background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold',
              mb: 3
            }}>
              ⚙️ Configurações da Playlist
            </Typography>

            <Box sx={{
              background: 'rgba(0,123,255,0.05)',
              borderRadius: '12px',
              p: 3
            }}>
              <PlaylistOrientationConfig
                playlistConfig={playlistConfig}
                onConfigChange={onConfigChange}
                disabled={false}
              />
            </Box>
          </Box>
        );

      case 4:
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{
              background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold',
              mb: 3
            }}>
              {isEditing ? '✏️ Resumo das Alterações' : '✅ Resumo da Nova Playlist'}
            </Typography>

            {isEditing && (
              <Box sx={{
                mb: 3,
                p: 2,
                borderRadius: '8px',
                background: 'linear-gradient(135deg, rgba(255,193,7,0.1) 0%, rgba(255,152,0,0.1) 100%)',
                border: '1px solid rgba(255,193,7,0.3)'
              }}>
                <Typography variant="body2" color="warning.main" fontWeight="bold">
                  ⚠️ Você está editando uma playlist existente. As alterações serão salvas permanentemente.
                </Typography>
              </Box>
            )}

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, borderRadius: '12px', background: 'rgba(0,123,255,0.05)' }}>
                  <Typography variant="h6" gutterBottom color="primary">
                    📋 Informações
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    <strong>Nome:</strong> {playlistInfo.nome || 'Sem nome'}
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    <strong>Descrição:</strong> {playlistInfo.descricao || 'Sem descrição'}
                  </Typography>
                  <Typography variant="body1">
                    <strong>Total de mídias:</strong> {selectedMidias.length}
                  </Typography>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, borderRadius: '12px', background: 'rgba(0,196,159,0.05)' }}>
                  <Typography variant="h6" gutterBottom color="secondary">
                    ⚙️ Configurações
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    <strong>Orientação:</strong> {playlistConfig.orientation || 'Mista'}
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    <strong>Transição:</strong> {playlistConfig.transitionDuration || 500}ms
                  </Typography>
                  <Typography variant="body1">
                    <strong>Rotação automática:</strong> {playlistConfig.autoRotate ? 'Sim' : 'Não'}
                  </Typography>
                </Paper>
              </Grid>

              <Grid item xs={12}>
                <Paper sx={{ p: 3, borderRadius: '12px' }}>
                  <Typography variant="h6" gutterBottom>
                    🎬 Mídias Selecionadas
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedMidias.map((midia, index) => (
                      <Chip
                        key={midia.id}
                        label={`${index + 1}. ${midia.nome}`}
                        color={midia.type?.startsWith('video/') ? 'primary' : 'secondary'}
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        );

      default:
        return <Box sx={{ p: 3 }}>Conteúdo do step {activeStep}</Box>;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '16px',
          background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.95) 100%)',
          backdropFilter: 'blur(10px)',
          minHeight: '80vh'
        }
      }}
    >
      <DialogTitle sx={{
        background: 'linear-gradient(135deg, #007BFF 0%, #00C49F 100%)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        borderRadius: '16px 16px 0 0'
      }}>
        <PlaylistAddIcon />
        <Typography variant="h6" fontWeight="bold">
          {isEditing ? `Editar Playlist: ${playlistData.nome}` : 'Criar Nova Playlist'}
        </Typography>
        <Box sx={{ flexGrow: 1 }} />
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3, borderBottom: '1px solid rgba(0,0,0,0.1)' }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  icon={step.icon}
                  sx={{
                    '& .MuiStepIcon-root': {
                      color: index <= activeStep ? '#007BFF' : 'rgba(0,0,0,0.3)',
                      '&.Mui-active': {
                        color: '#007BFF',
                      },
                      '&.Mui-completed': {
                        color: '#00C49F',
                      },
                    },
                  }}
                >
                  {step.label}
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <Fade in timeout={300}>
          <Box>
            {renderStepContent()}
          </Box>
        </Fade>

        <Box sx={{
          p: 3,
          borderTop: '1px solid rgba(0,0,0,0.1)',
          display: 'flex',
          justifyContent: 'space-between',
          background: 'rgba(0,0,0,0.02)'
        }}>
          <Button
            onClick={handleBack}
            disabled={activeStep === 0}
            variant="outlined"
            sx={{ borderRadius: '8px' }}
          >
            Voltar
          </Button>

          <Button
            onClick={activeStep === steps.length - 1 ? () => onSave(playlistInfo) : handleNext}
            variant="contained"
            sx={{
              borderRadius: '8px',
              background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #0056b3 30%, #00a085 90%)',
              }
            }}
          >
            {activeStep === steps.length - 1 ? (isEditing ? 'Salvar Alterações' : 'Criar Playlist') : 'Próximo'}
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default PlaylistCreator;
