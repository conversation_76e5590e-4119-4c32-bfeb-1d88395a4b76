{"name": "mod", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"vite\" \"node src/server/index.js\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node src/server/index.js"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^5.15.3", "@mui/material": "^5.15.3", "@mui/x-date-pickers": "^6.18.7", "cors": "^2.8.5", "date-fns": "^2.30.0", "express": "^4.18.2", "formik": "^2.4.6", "mongodb": "^6.2.0", "multer": "^1.4.5-lts.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^8.2.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "mongodb-memory-server": "^10.1.4", "vite": "^6.3.5"}}