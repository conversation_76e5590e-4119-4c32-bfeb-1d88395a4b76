import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Tabs,
  Tab,
  Paper,
  InputAdornment,
  Alert,
  Stack,
  Tooltip,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Divider,
} from '@mui/material';
import Navbar from '../../components/Navbar/Navbar';
import { useFormik } from 'formik';
import * as yup from 'yup';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import PlaylistAddIcon from '@mui/icons-material/PlaylistAdd';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { optimizeMediaForStorage, saveMediaToStorage, savePlaylistsToStorage, loadMediaFromStorage, loadPlaylistsFromStorage } from '../../utils/storageUtils';
import { httpService } from '../../services/httpService';
import MediaRotationConfig from '../../components/MediaRotationConfig/MediaRotationConfig';
import PlaylistOrientationConfig from '../../components/PlaylistOrientationConfig/PlaylistOrientationConfig';
import { useMediaRotation } from '../../hooks/useRotation';
import {
  getCurrentUser
} from '../../utils/userDataUtils';

const validationSchema = yup.object({
  nome: yup
    .string()
    .min(3, 'Nome deve ter no mínimo 3 caracteres')
    .required('Nome é obrigatório'),
});

const playlistValidationSchema = yup.object({
  nome: yup
    .string()
    .min(3, 'Nome deve ter no mínimo 3 caracteres')
    .required('Nome é obrigatório'),
  descricao: yup
    .string()
    .min(5, 'Descrição deve ter no mínimo 5 caracteres')
    .required('Descrição é obrigatória'),
});

const GerenciamentoMidias = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [midias, setMidias] = useState([]);
  const [playlists, setPlaylists] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [playlistDialogOpen, setPlaylistDialogOpen] = useState(false);
  const [selectedMidia, setSelectedMidia] = useState(null);
  const [selectedPlaylist, setSelectedPlaylist] = useState(null);
  const [previewFile, setPreviewFile] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMidiasForPlaylist, setSelectedMidiasForPlaylist] = useState([]);
  const [selectMidiasDialogOpen, setSelectMidiasDialogOpen] = useState(false);
  const [searchMidiasTerm, setSearchMidiasTerm] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [videoDuration, setVideoDuration] = useState(null);

  // Estados para configuração de rotação
  const [mediaRotationConfig, setMediaRotationConfig] = useState({
    rotation: 0,
    orientation: 'auto',
    fitMode: 'cover',
    duration: 10
  });
  const [playlistOrientationConfig, setPlaylistOrientationConfig] = useState({
    orientation: 'mixed',
    autoRotate: false,
    transitionDuration: 500,
    adaptToContent: true,
    globalRotation: 0
  });

  // Carregar dados iniciais
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Verificar se usuário está logado
        const currentUser = getCurrentUser();
        if (!currentUser) {
          setSnackbar({
            open: true,
            message: 'Usuário não está logado. Redirecionando...',
            severity: 'error'
          });
          return;
        }

        console.log(`👤 Carregando dados para usuário: ${currentUser.name} (${currentUser.email})`);

        // Carregar dados do banco de dados
        console.log('🗄️ Carregando dados do banco de dados...');
        const [dbMidias, dbPlaylists] = await Promise.all([
          httpService.loadMedias(),
          httpService.loadPlaylists()
        ]);

        console.log('🗄️ Dados carregados do banco:', { midias: dbMidias.length, playlists: dbPlaylists.length });
        console.log('🗄️ Playlists com configurações:', dbPlaylists.map(p => ({
          id: p.id,
          nome: p.nome,
          orientation: p.orientation,
          autoRotate: p.autoRotate,
          transitionDuration: p.transitionDuration,
          adaptToContent: p.adaptToContent,
          globalRotation: p.globalRotation
        })));

        setMidias(dbMidias || []);
        setPlaylists(dbPlaylists || []);
      } catch (error) {
        console.error('Erro ao carregar dados iniciais:', error);
        setSnackbar({
          open: true,
          message: 'Erro ao carregar dados. Verifique a conexão com o servidor.',
          severity: 'error'
        });
      }
    };

    loadInitialData();
  }, []);

  // Removido o useEffect que salvava automaticamente no localStorage
  // Agora as mídias e playlists são gerenciadas apenas pelo banco de dados

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const isValidFormat = ['image/jpeg', 'image/png', 'video/mp4'].includes(file.type);
      if (!isValidFormat) {
        setSnackbar({
          open: true,
          message: 'Formato de arquivo não suportado. Use apenas JPG, PNG ou MP4.',
          severity: 'error'
        });
        return;
      }

      // Verificar tamanho do arquivo
      const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
      if (file.size > MAX_FILE_SIZE) {
        setSnackbar({
          open: true,
          message: 'O arquivo é muito grande. O tamanho máximo é 50MB.',
          severity: 'error'
        });
        return;
      }

      // Converter tanto vídeos quanto imagens para base64 para persistência
      setVideoDuration(null); // Limpar duração de vídeo anterior

      const reader = new FileReader();
      reader.onloadend = () => {
        const previewData = {
          file,
          preview: reader.result, // Base64 data URL
          type: file.type,
          size: file.size,
          lastModified: file.lastModified,
          name: file.name
        };

        setPreviewFile(previewData);
        console.log('🎬 Preview file criado:', previewData);
        console.log('📊 Tamanho do preview (base64):', reader.result.length);

        // Se for vídeo, detectar duração
        if (file.type.startsWith('video/')) {
          const video = document.createElement('video');
          video.preload = 'metadata';
          video.onloadedmetadata = () => {
            const duration = Math.ceil(video.duration);
            setVideoDuration(duration);
            console.log(`🎬 Duração do vídeo detectada: ${duration}s`);

            // Atualizar configuração de mídia com duração do vídeo
            setMediaRotationConfig(prev => ({
              ...prev,
              duration: duration
            }));
          };
          video.onerror = () => {
            console.error('Erro ao carregar metadados do vídeo');
            setVideoDuration(null);
          };
          video.src = reader.result; // Usar base64 para detectar duração
        }
      };

      reader.onerror = () => {
        console.error('Erro ao ler arquivo');
        setSnackbar({
          open: true,
          message: 'Erro ao processar arquivo. Tente novamente.',
          severity: 'error'
        });
      };

      reader.readAsDataURL(file);
    }
  };

  const formik = useFormik({
    initialValues: {
      nome: '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values, { resetForm }) => {
      if (!previewFile) {
        setSnackbar({
          open: true,
          message: 'Por favor, selecione um arquivo de mídia.',
          severity: 'error'
        });
        return;
      }

      try {
        console.log('Iniciando processo de upload/edição de mídia...');

        const novaMidia = {
          id: selectedMidia ? selectedMidia.id : Date.now(),
          nome: values.nome,
          type: previewFile.type,
          size: previewFile.size,
          lastModified: previewFile.lastModified,
          preview: previewFile.preview, // Base64 data URL
          originalUrl: previewFile.preview, // Para compatibilidade
          // Adicionar configurações de rotação
          rotation: mediaRotationConfig.rotation,
          orientation: mediaRotationConfig.orientation,
          fitMode: mediaRotationConfig.fitMode,
          duration: mediaRotationConfig.duration,
          // Manter arquivo para compatibilidade
          arquivo: {
            preview: previewFile.preview,
            type: previewFile.type,
            size: previewFile.size,
            lastModified: previewFile.lastModified,
            name: previewFile.name
          }
        };

        console.log('Dados da nova mídia:', novaMidia);

        // Salvar no banco de dados
        console.log('🗄️ Salvando mídia no banco de dados...');
        let savedMidia;

        if (selectedMidia) {
          console.log('Editando mídia existente no banco...');
          savedMidia = await httpService.updateMedia(selectedMidia.id, novaMidia);
        } else {
          console.log('Criando nova mídia no banco...');
          savedMidia = await httpService.saveMedia(novaMidia);
        }

        // Atualizar estado local
        let updatedMidias;
        if (selectedMidia) {
          updatedMidias = midias.map(midia => midia.id === selectedMidia.id ? savedMidia : midia);
        } else {
          updatedMidias = [...midias, savedMidia];
        }

        setMidias(updatedMidias);
        console.log('✅ Mídia salva no banco de dados:', savedMidia);

        resetForm();
        setSelectedMidia(null);

        // Limpar URL de objeto se necessário
        if (previewFile?.isObjectUrl && previewFile?.preview) {
          URL.revokeObjectURL(previewFile.preview);
        }
        setPreviewFile(null);

        // Reset configurações de rotação
        setMediaRotationConfig({
          rotation: 0,
          orientation: 'auto',
          fitMode: 'cover',
          duration: 10
        });
        setVideoDuration(null);

        setDialogOpen(false);

        setSnackbar({
          open: true,
          message: selectedMidia ? 'Mídia editada com sucesso!' : 'Mídia criada com sucesso!',
          severity: 'success'
        });
      } catch (error) {
        console.error('Erro ao processar mídia:', error);
        setSnackbar({
          open: true,
          message: 'Erro ao processar mídia. Tente novamente.',
          severity: 'error'
        });
      }
    },
  });

  const playlistFormik = useFormik({
    initialValues: {
      nome: '',
      descricao: '',
    },
    validationSchema: playlistValidationSchema,
    onSubmit: async (values, { resetForm }) => {
      if (selectedMidiasForPlaylist.length === 0) {
        alert('Por favor, adicione pelo menos uma mídia à playlist.');
        return;
      }

      console.log('🎵 Salvando playlist com configurações:', {
        values,
        playlistOrientationConfig
      });

      const novaPlaylist = {
        id: selectedPlaylist ? selectedPlaylist.id : Date.now(),
        ...values,
        midias: selectedMidiasForPlaylist,
        // Adicionar configurações de orientação
        orientation: playlistOrientationConfig.orientation,
        autoRotate: playlistOrientationConfig.autoRotate,
        transitionDuration: playlistOrientationConfig.transitionDuration,
        adaptToContent: playlistOrientationConfig.adaptToContent,
        globalRotation: playlistOrientationConfig.globalRotation,
      };

      console.log('🎵 Nova playlist criada:', novaPlaylist);
      console.log('🔧 Configurações específicas sendo salvas:', {
        orientation: novaPlaylist.orientation,
        autoRotate: novaPlaylist.autoRotate,
        transitionDuration: novaPlaylist.transitionDuration,
        adaptToContent: novaPlaylist.adaptToContent,
        globalRotation: novaPlaylist.globalRotation
      });

      // Salvar no banco de dados
      console.log('🗄️ Salvando playlist no banco de dados...');
      let savedPlaylist;

      if (selectedPlaylist) {
        console.log('Editando playlist existente no banco...');
        savedPlaylist = await httpService.updatePlaylist(selectedPlaylist.id, novaPlaylist);
      } else {
        console.log('Criando nova playlist no banco...');
        savedPlaylist = await httpService.savePlaylist(novaPlaylist);
      }

      // Atualizar estado local
      if (selectedPlaylist) {
        setPlaylists(playlists.map(playlist =>
          playlist.id === selectedPlaylist.id ? savedPlaylist : playlist
        ));
      } else {
        setPlaylists([...playlists, savedPlaylist]);
      }

      console.log('✅ Playlist salva no banco de dados:', savedPlaylist);

      resetForm();
      setSelectedPlaylist(null);
      setSelectedMidiasForPlaylist([]);

      // Reset configurações de orientação para valores padrão
      console.log('🔄 Resetando configurações após salvar');
      setPlaylistOrientationConfig({
        orientation: 'mixed',
        autoRotate: false,
        transitionDuration: 500,
        adaptToContent: true,
        globalRotation: 0
      });

      setPlaylistDialogOpen(false);
    },
  });

  const handleDelete = async (id) => {
    try {
      console.log(`Tentando excluir mídia com ID: ${id}`);

      // Excluir do banco de dados
      console.log('🗄️ Excluindo mídia do banco de dados...');
      await httpService.deleteMedia(id);
      console.log('✅ Mídia excluída do banco de dados');

      // Atualizar estado local
      const updatedMidias = midias.filter(midia => midia.id !== id);
      setMidias(updatedMidias);

      setSnackbar({
        open: true,
        message: 'Mídia excluída com sucesso!',
        severity: 'success'
      });

      console.log(`Mídia ${id} excluída com sucesso`);
    } catch (error) {
      console.error('Erro ao excluir mídia:', error);
      setSnackbar({
        open: true,
        message: 'Erro ao excluir mídia. Tente novamente.',
        severity: 'error'
      });
    }
  };

  const handleEdit = (midia) => {
    setSelectedMidia(midia);

    // Preservar o preview corretamente
    const previewToSet = {
      preview: midia.preview || midia.originalUrl || '',
      type: midia.type || '',
      size: midia.size || 0,
      lastModified: midia.lastModified || Date.now(),
      name: midia.arquivo?.name || midia.nome || 'media'
    };

    console.log('🔧 Editando mídia:', midia);
    console.log('🔧 Preview a ser definido:', previewToSet);

    setPreviewFile(previewToSet);
    formik.setValues({
      nome: midia.nome,
    });

    // Carregar configurações de rotação existentes
    setMediaRotationConfig({
      rotation: midia.rotation || 0,
      orientation: midia.orientation || 'auto',
      fitMode: midia.fitMode || 'cover',
      duration: midia.duration || 10
    });

    // Se for vídeo, tentar carregar a duração
    if (midia.type && midia.type.startsWith('video/')) {
      // Se já tem duração salva, usar ela
      if (midia.duration && midia.duration > 10) {
        setVideoDuration(midia.duration);
      } else {
        // Tentar detectar duração do vídeo existente
        const videoUrl = midia.preview || midia.originalUrl;
        if (videoUrl) {
          const video = document.createElement('video');
          video.preload = 'metadata';
          video.onloadedmetadata = () => {
            const duration = Math.ceil(video.duration);
            setVideoDuration(duration);
            console.log(`🎬 Duração do vídeo existente detectada: ${duration}s`);
          };
          video.onerror = () => {
            console.error('Erro ao carregar metadados do vídeo existente');
            setVideoDuration(midia.duration || null);
          };
          video.src = videoUrl;
        } else {
          setVideoDuration(midia.duration || null);
        }
      }
    } else {
      setVideoDuration(null);
    }

    setDialogOpen(true);
  };

  const handleDeletePlaylist = async (id) => {
    try {
      console.log(`Tentando excluir playlist com ID: ${id}`);

      // Excluir do banco de dados
      console.log('🗄️ Excluindo playlist do banco de dados...');
      await httpService.deletePlaylist(id);
      console.log('✅ Playlist excluída do banco de dados');

      // Atualizar estado local
      const updatedPlaylists = playlists.filter(playlist => playlist.id !== id);
      setPlaylists(updatedPlaylists);

      setSnackbar({
        open: true,
        message: 'Playlist excluída com sucesso!',
        severity: 'success'
      });

      console.log(`Playlist ${id} excluída com sucesso`);
    } catch (error) {
      console.error('Erro ao excluir playlist:', error);
      setSnackbar({
        open: true,
        message: 'Erro ao excluir playlist. Tente novamente.',
        severity: 'error'
      });
    }
  };

  const handleEditPlaylist = (playlist) => {
    console.log('✏️ Editando playlist:', playlist);
    console.log('✏️ Configurações da playlist no banco:', {
      orientation: playlist.orientation,
      autoRotate: playlist.autoRotate,
      transitionDuration: playlist.transitionDuration,
      adaptToContent: playlist.adaptToContent,
      globalRotation: playlist.globalRotation
    });

    setSelectedPlaylist(playlist);
    setSelectedMidiasForPlaylist(playlist.midias || []);
    playlistFormik.setValues({
      nome: playlist.nome,
      descricao: playlist.descricao || '',
    });

    // Carregar configurações de orientação existentes
    const configToLoad = {
      orientation: playlist.orientation || 'mixed',
      autoRotate: playlist.autoRotate || false,
      transitionDuration: playlist.transitionDuration || 500,
      adaptToContent: playlist.adaptToContent !== undefined ? playlist.adaptToContent : true,
      globalRotation: playlist.globalRotation || 0
    };

    console.log('✏️ Configurações que serão carregadas no estado:', configToLoad);
    setPlaylistOrientationConfig(configToLoad);

    setPlaylistDialogOpen(true);
  };

  const handleAddMidiaToPlaylist = async (midia) => {
    // Determinar se é um vídeo de forma mais robusta
    const isVideo =
      (midia.type && midia.type.startsWith('video/')) ||
      (midia.arquivo && midia.arquivo.type && midia.arquivo.type.startsWith('video/'));

    // Duração padrão: 10s para imagens, será atualizada para vídeos
    const midiaComConfig = {
      ...midia,
      duracao: 10, // Duração padrão para imagens
      ordem: selectedMidiasForPlaylist.length + 1
    };

    if (isVideo) {
      try {
        // Determinar a URL do vídeo
        let videoUrl = '';
        if (midia.originalUrl) {
          videoUrl = midia.originalUrl;
        } else if (midia.preview) {
          videoUrl = midia.preview;
        } else if (midia.arquivo && midia.arquivo.preview) {
          videoUrl = midia.arquivo.preview;
        }

        if (videoUrl) {
          const video = document.createElement('video');
          video.crossOrigin = "anonymous";
          video.preload = "metadata";
          video.src = videoUrl;

          // Definir um timeout para evitar espera infinita
          const timeoutPromise = new Promise((resolve) => {
            setTimeout(() => {
              console.warn(`Timeout ao carregar metadados do vídeo ${midia.nome}`);
              resolve(10); // Duração padrão em caso de timeout
            }, 5000);
          });

          // Promessa para carregar os metadados do vídeo
          const metadataPromise = new Promise((resolve) => {
            video.onloadedmetadata = () => {
              console.log(`Duração do vídeo ${midia.nome}: ${video.duration}s`);
              resolve(Math.ceil(video.duration));
            };

            video.onerror = (e) => {
              console.error(`Erro ao carregar metadados do vídeo ${midia.nome}:`, e);
              resolve(10); // Duração padrão em caso de erro
            };
          });

          // Aguardar a primeira promessa que resolver
          const duracao = await Promise.race([metadataPromise, timeoutPromise]);
          midiaComConfig.duracao = duracao;

          // Garantir que o arquivo está presente na mídia
          if (!midiaComConfig.arquivo && midia.preview) {
            try {
              // Converter base64/dataURL para File
              const response = await fetch(midia.preview);
              const blob = await response.blob();
              midiaComConfig.arquivo = new File([blob], midia.nome || 'video.mp4', { type: midia.type || 'video/mp4' });
            } catch (error) {
              console.error('Erro ao converter preview para arquivo:', error);
            }
          }

          setSelectedMidiasForPlaylist(prev => [...prev, midiaComConfig]);
        } else {
          // Se não conseguir encontrar a URL do vídeo, adicionar com duração padrão
          console.warn(`Não foi possível encontrar URL para o vídeo ${midia.nome}`);
          setSelectedMidiasForPlaylist(prev => [...prev, midiaComConfig]);
        }
      } catch (error) {
        console.error('Erro ao processar vídeo para playlist:', error);
        // Adicionar mesmo com erro, usando configuração padrão
        setSelectedMidiasForPlaylist(prev => [...prev, midiaComConfig]);
      }
    } else {
      // Para imagens, adicionar diretamente com duração padrão
      setSelectedMidiasForPlaylist(prev => [...prev, midiaComConfig]);
    }
  };

  const handleOpenSelectMidiasDialog = () => {
    setSelectMidiasDialogOpen(true);
  };

  const handleCloseSelectMidiasDialog = () => {
    setSelectMidiasDialogOpen(false);
    setSearchMidiasTerm('');
  };

  const filteredMidiasForDialog = midias.filter(midia =>
    midia.nome.toLowerCase().includes(searchMidiasTerm.toLowerCase())
  );

  const handleRemoveMidiaFromPlaylist = (id) => {
    setSelectedMidiasForPlaylist(prev => {
      const newList = prev.filter(midia => midia.id !== id);
      return newList.map((midia, index) => ({
        ...midia,
        ordem: index + 1
      }));
    });
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(selectedMidiasForPlaylist);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setSelectedMidiasForPlaylist(items.map((midia, index) => ({
      ...midia,
      ordem: index + 1
    })));
  };

  const filteredMidias = midias.filter(midia =>
    midia.nome.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <>
      <Navbar />
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity || 'info'}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4, position: 'relative' }}>
        <Paper sx={{ mb: 4, borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)' }}>
            <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            textColor="primary"
            indicatorColor="primary"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="Galeria de Mídias" />
            <Tab label="Playlists" />
          </Tabs>
        </Paper>

        {/* Galeria de Mídias */}
        {activeTab === 0 && (
          <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h4" component="h1" sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                Galeria de Mídias
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  placeholder="Buscar mídia..."
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '8px',
                      '&:hover fieldset': {
                        borderColor: '#007BFF',
                      },
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
                <Button
                  variant="contained"
                  onClick={() => setDialogOpen(true)}
                  startIcon={<CloudUploadIcon />}
                  sx={{
                    bgcolor: '#007BFF',
                    '&:hover': { bgcolor: '#0056b3' },
                  }}
                >
                  Nova Mídia
                </Button>
              </Box>
            </Box>

            {filteredMidias.length === 0 ? (
              <Alert severity="info" sx={{ mt: 2 }}>
                Nenhuma mídia encontrada. Clique em "Nova Mídia" para adicionar conteúdo à galeria.
              </Alert>
            ) : (
              <Grid container spacing={3}>
                {filteredMidias.map((midia) => (
                  <Grid item xs={12} sm={6} md={4} key={midia.id}>
                    <Card elevation={3} sx={{
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: '0 8px 16px rgba(0, 0, 0, 0.2)'
                      },
                      borderRadius: '12px',
                      overflow: 'hidden',
                      background: 'linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 196, 159, 0.1) 100%)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      backdropFilter: 'blur(10px)'
                    }}>
                      {/* Verificação corrigida para determinar o tipo de mídia */}
                      {midia.type && midia.type.startsWith('image/') ? (
                        <CardMedia
                          component="img"
                          height="200"
                          image={midia.preview || midia.originalUrl || (midia.arquivo && (midia.arquivo.preview || midia.arquivo.originalUrl)) || ''}
                          alt={midia.nome}
                        />
                      ) : (
                        <CardMedia
                          component="video"
                          height="200"
                          src={midia.preview || midia.originalUrl || ''}
                          controls
                          preload="metadata"
                          onError={(e) => {
                            console.error(`Erro ao carregar vídeo ${midia.nome}:`, e);
                            console.log('Dados da mídia:', midia);
                            console.log('Preview URL:', midia.preview);
                            console.log('Original URL:', midia.originalUrl);
                            console.log('Tipo:', midia.type);
                            console.log('URL atual do vídeo:', e.target.src);
                          }}
                        />
                      )}
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom sx={{
                          fontWeight: 600,
                          background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent'
                        }}>
                          {midia.nome}
                        </Typography>
                        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', gap: 1 }}>
                          <IconButton
                            onClick={() => handleEdit(midia)}
                            color="primary"
                            sx={{
                              transition: 'transform 0.2s',
                              '&:hover': { transform: 'scale(1.1)' }
                            }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            onClick={() => handleDelete(midia.id)}
                            color="error"
                            sx={{
                              transition: 'transform 0.2s',
                              '&:hover': { transform: 'scale(1.1)' }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </>
        )}

        {/* Playlists */}
        {activeTab === 1 && (
          <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h4" component="h1" gutterBottom sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                Gerenciamento de Playlists
              </Typography>
              <Button
                variant="contained"
                onClick={() => {
                  setSelectedPlaylist(null);
                  setSelectedMidiasForPlaylist([]);
                  playlistFormik.resetForm();
                  setPlaylistDialogOpen(true);
                }}
                sx={{
                  bgcolor: '#28a745',
                  '&:hover': { bgcolor: '#218838' },
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                }}
                startIcon={<PlaylistAddIcon />}
              >
                Nova Playlist
              </Button>
            </Box>

            {playlists.length === 0 ? (
              <Alert severity="info" sx={{ mt: 2 }}>
                Nenhuma playlist encontrada. Clique em "Nova Playlist" para criar uma.
              </Alert>
            ) : (
              <Grid container spacing={3}>
                {playlists.map((playlist) => (
                  <Grid item xs={12} sm={6} md={4} key={playlist.id}>
                    <Card elevation={3} sx={{
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: '0 8px 16px rgba(0, 0, 0, 0.2)'
                      },
                      borderRadius: '12px',
                      overflow: 'hidden',
                      background: 'linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 196, 159, 0.1) 100%)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      backdropFilter: 'blur(10px)'
                    }}>
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom sx={{
                          fontWeight: 600,
                          background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent'
                        }}>
                          {playlist.nome}
                        </Typography>
                        <Typography color="textSecondary" gutterBottom>
                          {playlist.descricao}
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          {playlist.midias?.length || 0} mídias na playlist
                        </Typography>
                        <Box sx={{ mt: 2 }}>
                          <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                            {playlist.midias?.map((midia, index) => (
                              <Tooltip key={midia.id} title={midia.nome}>
                                {/* Verificação corrigida para determinar o tipo de mídia */}
                                {midia.type && midia.type.startsWith('image/') ? (
                                  <Box
                                    component="img"
                                    src={midia.preview || midia.originalUrl || (midia.arquivo && (midia.arquivo.preview || midia.arquivo.originalUrl)) || ''}
                                    alt={midia.nome}
                                    sx={{
                                      width: 40,
                                      height: 40,
                                      borderRadius: 1,
                                      objectFit: 'cover',
                                    }}
                                  />
                                ) : (
                                  <Box
                                    sx={{
                                      width: 40,
                                      height: 40,
                                      borderRadius: 1,
                                      bgcolor: 'action.selected',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                    }}
                                  >
                                    <Typography variant="caption">{index + 1}</Typography>
                                  </Box>
                                )}
                              </Tooltip>
                            ))}
                          </Stack>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <IconButton
                            onClick={() => handleEditPlaylist(playlist)}
                            color="primary"
                            sx={{
                              transition: 'transform 0.2s',
                              '&:hover': { transform: 'scale(1.1)' }
                            }}
                          >
                            <EditIcon />
                          </IconButton>
                            <IconButton
                            onClick={() => handleDeletePlaylist(playlist.id)}
                            color="error"
                            sx={{
                              transition: 'transform 0.2s',
                              '&:hover': { transform: 'scale(1.1)' }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </>
        )}

        {/* Dialog para criar/editar mídia */}
        <Dialog
            open={dialogOpen}
            onClose={() => {
              setDialogOpen(false);
              setSelectedMidia(null);

              // Limpar URL de objeto se necessário
              if (previewFile?.isObjectUrl && previewFile?.preview) {
                URL.revokeObjectURL(previewFile.preview);
              }
              setPreviewFile(null);
              formik.resetForm();
            }}
            maxWidth="sm"
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: '12px',
                boxShadow: '0 8px 16px rgba(0, 0, 0, 0.2)',
                background: 'linear-gradient(to bottom right, #ffffff, #fafafa)'
              }
            }}
          >
          <DialogTitle>
            {selectedMidia ? 'Editar Mídia' : 'Nova Mídia'}
          </DialogTitle>
          <form onSubmit={formik.handleSubmit}>
            <DialogContent>
              <Box sx={{ mb: 3, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                {previewFile ? (
                  previewFile.type.startsWith('image/') ? (
                    <img
                      src={previewFile.preview}
                      alt="Preview"
                      style={{ maxWidth: '100%', maxHeight: 200, objectFit: 'contain' }}
                    />
                  ) : (
                    <video
                      src={previewFile.preview}
                      style={{ maxWidth: '100%', maxHeight: 200 }}
                      controls
                      preload="metadata"
                      onError={(e) => {
                        console.error('Erro ao carregar preview de vídeo:', e);
                        console.log('Preview file:', previewFile);
                        console.log('Preview URL:', previewFile.preview);
                        console.log('URL atual do vídeo:', e.target.src);
                      }}
                    />
                  )
                ) : null}
                <Button
                  component="label"
                  variant="outlined"
                  sx={{ mt: 2 }}
                  startIcon={<CloudUploadIcon />}
                >
                  {selectedMidia ? 'Trocar Mídia' : 'Upload Mídia'}
                  <input
                    type="file"
                    hidden
                    accept="image/jpeg,image/png,video/mp4"
                    onChange={handleFileChange}
                  />
                </Button>
              </Box>

              <TextField
                fullWidth
                margin="normal"
                id="nome"
                name="nome"
                label="Nome da Mídia"
                value={formik.values.nome}
                onChange={formik.handleChange}
                error={formik.touched.nome && Boolean(formik.errors.nome)}
                helperText={formik.touched.nome && formik.errors.nome}
              />

              {/* Configurações de Rotação */}
              <Box sx={{ mt: 2 }}>
                <MediaRotationConfig
                  mediaConfig={mediaRotationConfig}
                  onConfigChange={setMediaRotationConfig}
                  disabled={!previewFile}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => {
                  setDialogOpen(false);
                  setSelectedMidia(null);

                  // Limpar URL de objeto se necessário
                  if (previewFile?.isObjectUrl && previewFile?.preview) {
                    URL.revokeObjectURL(previewFile.preview);
                  }
                  setPreviewFile(null);
                  setVideoDuration(null);
                  formik.resetForm();
                }}
                sx={{
                  color: '#666',
                  '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.05)' }
                }}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                variant="contained"
                sx={{
                  bgcolor: '#007BFF',
                  '&:hover': { bgcolor: '#0056b3' },
                  px: 4
                }}
              >
                {selectedMidia ? 'Salvar' : 'Criar'}
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Dialog para criar/editar playlist */}
        <Dialog
            open={playlistDialogOpen}
            onClose={() => {
              setPlaylistDialogOpen(false);
              setSelectedPlaylist(null);
              setSelectedMidiasForPlaylist([]);
              playlistFormik.resetForm();
            }}
            maxWidth="md"
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: '12px',
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
              }
            }}
          >
          <DialogTitle sx={{
            background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 'bold',
            pb: 1
          }}>
            {selectedPlaylist ? 'Editar Playlist' : 'Nova Playlist'}
          </DialogTitle>
          <form onSubmit={playlistFormik.handleSubmit}>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    margin="normal"
                    id="nome"
                    name="nome"
                    label="Nome da Playlist"
                    value={playlistFormik.values.nome}
                    onChange={playlistFormik.handleChange}
                    error={playlistFormik.touched.nome && Boolean(playlistFormik.errors.nome)}
                    helperText={playlistFormik.touched.nome && playlistFormik.errors.nome}
                  />
                  <TextField
                    fullWidth
                    margin="normal"
                    id="descricao"
                    name="descricao"
                    label="Descrição"
                    multiline
                    rows={2}
                    value={playlistFormik.values.descricao}
                    onChange={playlistFormik.handleChange}
                    error={playlistFormik.touched.descricao && Boolean(playlistFormik.errors.descricao)}
                    helperText={playlistFormik.touched.descricao && playlistFormik.errors.descricao}
                  />

                  {/* Configurações de Orientação da Playlist */}
                  <Box sx={{ mt: 2 }}>
                    <PlaylistOrientationConfig
                      playlistConfig={playlistOrientationConfig}
                      onConfigChange={setPlaylistOrientationConfig}
                      disabled={false}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={handleOpenSelectMidiasDialog}
                    >
                      Adicionar Mídias
                    </Button>
                  </Box>
                  <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable droppableId="playlist-items">
                      {(provided) => (
                        <List
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                        >
                          {selectedMidiasForPlaylist.map((midia, index) => (
                            <Draggable
                              key={midia.id}
                              draggableId={midia.id.toString()}
                              index={index}
                            >
                              {(provided, snapshot) => (
                                <ListItem
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  sx={{
                                    bgcolor: snapshot.isDragging ? 'action.hover' : 'background.paper',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 2,
                                    mb: 1,
                                    border: '1px solid',
                                    borderColor: 'divider',
                                    borderRadius: 1,
                                    '&:hover': {
                                      bgcolor: 'action.hover',
                                    },
                                  }}
                                >
                                  <ListItemIcon {...provided.dragHandleProps}>
                                    <DragIndicatorIcon />
                                  </ListItemIcon>
                                  {/* Verificação corrigida para determinar o tipo de mídia */}
                                  {midia.type && midia.type.startsWith('image/') ? (
                                    <Box
                                      component="img"
                                      src={midia.preview || midia.originalUrl || (midia.arquivo && (midia.arquivo.preview || midia.arquivo.originalUrl)) || ''}
                                      alt={midia.nome}
                                      sx={{
                                        width: 60,
                                        height: 60,
                                        borderRadius: 1,
                                        objectFit: 'cover',
                                      }}
                                    />
                                  ) : (
                                    <Box
                                      sx={{
                                        width: 60,
                                        height: 60,
                                        borderRadius: 1,
                                        bgcolor: 'action.selected',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                      }}
                                    >
                                      <Typography variant="body2">{index + 1}</Typography>
                                    </Box>
                                  )}
                                  <ListItemText
                                    primary={midia.nome}
                                    secondary={`Duração: ${midia.duracao}s`}
                                  />
                                  <ListItemSecondaryAction>
                                    <IconButton
                                      edge="end"
                                      aria-label="delete"
                                      onClick={() => handleRemoveMidiaFromPlaylist(midia.id)}
                                    >
                                      <DeleteIcon />
                                    </IconButton>
                                  </ListItemSecondaryAction>
                                </ListItem>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                        </List>
                      )}
                    </Droppable>
                  </DragDropContext>
                </Grid>
                <Grid item xs={12}>

                  <List>
                    {midias
                      .filter(midia => !selectedMidiasForPlaylist.some(m => m.id === midia.id))
                      .map((midia) => (
                        <ListItem
                          key={midia.id}
                          sx={{
                            bgcolor: 'background.paper',
                            mb: 1,
                            border: '1px solid',
                            borderColor: 'divider',
                            borderRadius: 1,
                          }}
                        >
                          <ListItemText
                            primary={midia.nome}
                          />
                          <ListItemSecondaryAction>
                            <IconButton
                              edge="end"
                              onClick={() => handleAddMidiaToPlaylist(midia)}
                              color="primary"
                            >
                              <AddIcon />
                            </IconButton>
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                  </List>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => {
                  setPlaylistDialogOpen(false);
                  setSelectedPlaylist(null);
                  setSelectedMidiasForPlaylist([]);
                  playlistFormik.resetForm();

                  // Reset configurações de orientação
                  setPlaylistOrientationConfig({
                    orientation: 'mixed',
                    autoRotate: false,
                    transitionDuration: 500,
                    adaptToContent: true,
                    globalRotation: 0
                  });
                }}
                sx={{
                  color: '#666',
                  '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.05)' }
                }}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                variant="contained"
                sx={{
                  background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #0056b3 30%, #00a67d 90%)',
                    transform: 'translateY(-1px)'
                  },
                  transition: 'all 0.3s ease',
                  px: 4
                }}
              >
                {selectedPlaylist ? 'Salvar' : 'Criar'}
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Dialog para seleção de mídias */}
        <Dialog
          open={selectMidiasDialogOpen}
          onClose={handleCloseSelectMidiasDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Selecionar Mídias</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              margin="normal"
              placeholder="Buscar mídia..."
              value={searchMidiasTerm}
              onChange={(e) => setSearchMidiasTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
            <List>
              {filteredMidiasForDialog.map((midia) => (
                <ListItem key={midia.id}>
                  <ListItemIcon>
                    {/* Verificação corrigida para determinar o tipo de mídia */}
                    {midia.type && midia.type.startsWith('image/') ? (
                      <img
                        src={midia.preview || midia.originalUrl || (midia.arquivo && (midia.arquivo.preview || midia.arquivo.originalUrl)) || ''}
                        alt={midia.nome}
                        style={{ width: 50, height: 50, objectFit: 'cover' }}
                      />
                    ) : (
                      <video
                        src={midia.originalUrl || midia.preview || (midia.arquivo && (midia.arquivo.preview || midia.arquivo.originalUrl)) || ''}
                        style={{ width: 50, height: 50 }}
                        preload="metadata"
                        onError={(e) => {
                          console.error(`Erro ao carregar vídeo em miniatura ${midia.nome}:`, e);
                          // Tentar recarregar com outra fonte se disponível
                          const video = e.target;
                          if (video.src !== midia.originalUrl && midia.originalUrl) {
                            video.src = midia.originalUrl;
                          } else if (video.src !== midia.preview && midia.preview) {
                            video.src = midia.preview;
                          } else if (midia.thumbnail) {
                            // Se tudo falhar, substituir por uma imagem da miniatura
                            const img = document.createElement('img');
                            img.src = midia.thumbnail;
                            img.alt = midia.nome;
                            img.style.width = '50px';
                            img.style.height = '50px';
                            img.style.objectFit = 'cover';
                            video.parentNode.replaceChild(img, video);
                          }
                        }}
                      />
                    )}
                  </ListItemIcon>
                  <ListItemText primary={midia.nome} />
                  <ListItemSecondaryAction>
                    <Tooltip title={selectedMidiasForPlaylist.some(m => m.id === midia.id) ? 'Mídia já adicionada' : 'Adicionar à playlist'}>
                      <span>
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => handleAddMidiaToPlaylist(midia)}
                          disabled={selectedMidiasForPlaylist.some(m => m.id === midia.id)}
                        >
                          {selectedMidiasForPlaylist.some(m => m.id === midia.id) ? 'Adicionado' : 'Adicionar'}
                        </Button>
                      </span>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseSelectMidiasDialog}>Concluir</Button>
          </DialogActions>
        </Dialog>
      </Container>
    </>
  );
};

export default GerenciamentoMidias;