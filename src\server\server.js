import express from 'express';
import cors from 'cors';
import multer from 'multer';
import { connectToMongo } from '../services/mongoService';
import { saveMediaToMongo, loadMediaFromMongo, savePlaylistToMongo, loadPlaylistsFromMongo, updatePlaylistInMongo, removeMediaFromMongo, removePlaylistFromMongo } from '../utils/mongoUtils';

const app = express();
const port = process.env.PORT || 3001;

// Configurações do middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Configuração do Multer para upload de arquivos
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: { fileSize: 50 * 1024 * 1024 } // Limite de 50MB
});

// Conectar ao MongoDB ao iniciar o servidor
connectToMongo().catch(console.error);

// Rotas para mídias
app.post('/api/media', upload.single('file'), async (req, res) => {
  try {
    const file = req.file;
    const mediaData = JSON.parse(req.body.mediaData);
    
    const media = {
      ...mediaData,
      file: {
        data: file.buffer,
        contentType: file.mimetype
      }
    };
    
    const savedMedia = await saveMediaToMongo(media);
    res.json(savedMedia);
  } catch (error) {
    console.error('Erro ao salvar mídia:', error);
    res.status(500).json({ error: 'Erro ao salvar mídia' });
  }
});

app.get('/api/media', async (req, res) => {
  try {
    const medias = await loadMediaFromMongo();
    res.json(medias);
  } catch (error) {
    console.error('Erro ao carregar mídias:', error);
    res.status(500).json({ error: 'Erro ao carregar mídias' });
  }
});

app.delete('/api/media/:id', async (req, res) => {
  try {
    await removeMediaFromMongo(req.params.id);
    res.json({ message: 'Mídia removida com sucesso' });
  } catch (error) {
    console.error('Erro ao remover mídia:', error);
    res.status(500).json({ error: 'Erro ao remover mídia' });
  }
});

// Rotas para playlists
app.post('/api/playlists', async (req, res) => {
  try {
    const playlist = req.body;
    const savedPlaylist = await savePlaylistToMongo(playlist);
    res.json(savedPlaylist);
  } catch (error) {
    console.error('Erro ao salvar playlist:', error);
    res.status(500).json({ error: 'Erro ao salvar playlist' });
  }
});

app.get('/api/playlists', async (req, res) => {
  try {
    const playlists = await loadPlaylistsFromMongo();
    res.json(playlists);
  } catch (error) {
    console.error('Erro ao carregar playlists:', error);
    res.status(500).json({ error: 'Erro ao carregar playlists' });
  }
});

app.put('/api/playlists/:id', async (req, res) => {
  try {
    const updatedPlaylist = await updatePlaylistInMongo(req.params.id, req.body);
    res.json(updatedPlaylist);
  } catch (error) {
    console.error('Erro ao atualizar playlist:', error);
    res.status(500).json({ error: 'Erro ao atualizar playlist' });
  }
});

app.delete('/api/playlists/:id', async (req, res) => {
  try {
    await removePlaylistFromMongo(req.params.id);
    res.json({ message: 'Playlist removida com sucesso' });
  } catch (error) {
    console.error('Erro ao remover playlist:', error);
    res.status(500).json({ error: 'Erro ao remover playlist' });
  }
});

// Iniciar o servidor
app.listen(port, () => {
  console.log(`Servidor rodando na porta ${port}`);
});