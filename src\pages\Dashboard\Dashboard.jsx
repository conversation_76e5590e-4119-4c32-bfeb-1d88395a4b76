import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Chip,
  Button,
  IconButton,
  Fade,
  LinearProgress,
  Snackbar,
  Alert,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import Navbar from '../../components/Navbar/Navbar';
import ScreenShareIcon from '@mui/icons-material/ScreenShare';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import TvIcon from '@mui/icons-material/Tv';
import MovieIcon from '@mui/icons-material/Movie';
import GroupIcon from '@mui/icons-material/Group';
import { loadMediaFromStorage, loadPlaylistsFromStorage } from '../../utils/storageUtils';
import {
  getCurrentUser,
  loadUserData,
  migrateGlobalDataToUser
} from '../../utils/userDataUtils';

const Dashboard = () => {
  const [telas, setTelas] = useState([]);
  const [midias, setMidias] = useState([]);
  const [playlists, setPlaylists] = useState([]);

  const [loading, setLoading] = useState(true);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    const loadData = async () => {
      try {
        // Verificar se usuário está logado
        const currentUser = getCurrentUser();
        if (!currentUser) {
          setSnackbar({
            open: true,
            message: 'Usuário não está logado. Redirecionando...',
            severity: 'error'
          });
          setLoading(false);
          return;
        }

        console.log(`👤 Dashboard - Carregando dados para usuário: ${currentUser.name} (${currentUser.email})`);

        // Migrar dados globais para dados do usuário (se necessário)
        migrateGlobalDataToUser('telas', 'telas');
        migrateGlobalDataToUser('midias', 'midias');
        migrateGlobalDataToUser('playlists', 'playlists');

        // Carregar dados específicos do usuário
        const userTelas = loadUserData('telas', []);
        const userMidias = loadUserData('midias', []);
        const userPlaylists = loadUserData('playlists', []);

        console.log(`📊 Dashboard - Dados carregados para ${currentUser.email}:`, {
          telas: userTelas.length,
          midias: userMidias.length,
          playlists: userPlaylists.length
        });

        setTelas(userTelas);
        setMidias(userMidias);
        setPlaylists(userPlaylists);
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        setSnackbar({
          open: true,
          message: 'Erro ao carregar dados do usuário.',
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const navigate = useNavigate();

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  const pieData = [
    { name: 'Telas Ativas', value: telas.length },
    { name: 'Mídias', value: midias.length },
    { name: 'Playlists', value: playlists.length },
  ];
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <>
      <Navbar />
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity || 'info'}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Fade in={!loading}>
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}>
                Dashboard
              </Typography>
              <Typography variant="subtitle1" color="text.secondary" sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                👤 Dados de: <strong>{getCurrentUser()?.name || 'Usuário'}</strong>
                {getCurrentUser()?.isAdmin && <span style={{ color: '#ff9800' }}>👑</span>}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                onClick={() => navigate('/gerenciamento-midias')}
                startIcon={<MovieIcon />}
                sx={{
                  bgcolor: '#28a745',
                  '&:hover': { bgcolor: '#218838' },
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                }}
              >
                Gerenciar Mídias
              </Button>
              <Button
                variant="contained"
                onClick={() => navigate('/gerenciamento-telas')}
                startIcon={<TvIcon />}
                sx={{
                  bgcolor: '#007BFF',
                  '&:hover': { bgcolor: '#0056b3' },
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                }}
              >
                Gerenciar Telas
              </Button>
            </Box>
          </Box>

      {loading ? (
        <Box sx={{ width: '100%', mt: 4 }}>
          <LinearProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Cards de Métricas */}
          <Grid item xs={12} md={4}>
            <Card elevation={3} sx={{
              background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
              color: 'white',
              transition: 'transform 0.3s',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TvIcon sx={{ fontSize: 40, mr: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Total de Telas
                  </Typography>
                </Box>
                <Typography variant="h3" sx={{ textAlign: 'center', mb: 1 }}>
                  {telas.length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card elevation={3} sx={{
              background: 'linear-gradient(45deg, #28a745 30%, #00C49F 90%)',
              color: 'white',
              transition: 'transform 0.3s',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <MovieIcon sx={{ fontSize: 40, mr: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Total de Mídias
                  </Typography>
                </Box>
                <Typography variant="h3" sx={{ textAlign: 'center', mb: 1 }}>
                  {midias.length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card elevation={3} sx={{
              background: 'linear-gradient(45deg, #FFBB28 30%, #FF8042 90%)',
              color: 'white',
              transition: 'transform 0.3s',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <GroupIcon sx={{ fontSize: 40, mr: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Total de Playlists
                  </Typography>
                </Box>
                <Typography variant="h3" sx={{ textAlign: 'center', mb: 1 }}>
                  {playlists.length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Visão geral das telas */}
          <Grid item xs={12} md={8}>
            <Paper elevation={3} sx={{
              p: 3,
              height: '100%',
              background: 'white',
              borderRadius: '12px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}>
              <Typography variant="h6" gutterBottom sx={{ mb: 3, color: '#333' }}>
                Telas Ativas
              </Typography>
              <List>
                {telas.map((tela) => (
                  <ListItem
                    key={tela.id}
                    sx={{
                      mb: 2,
                      bgcolor: '#f8f9fa',
                      borderRadius: '8px',
                      transition: 'transform 0.2s',
                      '&:hover': { transform: 'scale(1.01)' }
                    }}
                  >
                    <ListItemText
                      primary={<Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>{tela.nome}</Typography>}
                      secondary={`Local: ${tela.local} | Grupo: ${tela.grupo}`}
                    />
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Chip
                        icon={<PlayArrowIcon />}
                        label="Ativo"
                        color="success"
                        size="small"
                        sx={{ borderRadius: '8px' }}
                      />
                      <IconButton
                        size="small"
                        color="primary"
                        onClick={() => window.open(tela.url, '_blank')}
                      >
                        <ScreenShareIcon />
                      </IconButton>
                    </Box>
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>

          {/* Gráfico de Pizza */}
          <Grid item xs={12} md={4}>
            <Paper elevation={3} sx={{
              p: 3,
              height: '100%',
              background: 'white',
              borderRadius: '12px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}>
              <Typography variant="h6" gutterBottom sx={{ mb: 3, color: '#333' }}>
                Distribuição do Sistema
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
              <Box sx={{ mt: 2 }}>
                {pieData.map((entry, index) => (
                  <Box key={entry.name} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        bgcolor: COLORS[index % COLORS.length],
                        mr: 1
                      }}
                    />
                    <Typography variant="body2">
                      {entry.name}: {entry.value}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}
      </Box>
      </Fade>
    </Container>
    </>
  );
};

export default Dashboard;