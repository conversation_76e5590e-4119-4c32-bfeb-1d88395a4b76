// Constantes para rotação de tela e orientação

export const ROTATION_OPTIONS = [
  { value: 0, label: '0° (Normal)', icon: '↑' },
  { value: 90, label: '90° (<PERSON><PERSON><PERSON><PERSON>)', icon: '→' },
  { value: 180, label: '180° (Invertido)', icon: '↓' },
  { value: 270, label: '270° (Anti-horário)', icon: '←' },
  { value: 360, label: '360° (Completo)', icon: '↻' }
];

export const ORIENTATION_OPTIONS = [
  { value: 'horizontal', label: 'Horizontal (Paisagem)', icon: '📱' },
  { value: 'vertical', label: 'Vertical (Retrato)', icon: '📱' },
  { value: 'auto', label: 'Automático', icon: '🔄' }
];

export const PLAYLIST_ORIENTATION_OPTIONS = [
  { value: 'horizontal', label: 'Playlist Horizontal', description: 'Otimizada para telas em paisagem' },
  { value: 'vertical', label: 'Playlist Vertical', description: 'Otimizada para telas em retrato' },
  { value: 'mixed', label: 'Playlist Mista', description: 'Adapta-se automaticamente' }
];

// Funções utilitárias para rotação
export const getRotationCSS = (rotation) => {
  return {
    transform: `rotate(${rotation}deg)`,
    transformOrigin: 'center center'
  };
};

export const getOrientationCSS = (orientation, rotation = 0) => {
  const baseStyles = {
    transition: 'transform 0.5s ease-in-out'
  };

  switch (orientation) {
    case 'vertical':
      return {
        ...baseStyles,
        transform: `rotate(${rotation}deg)`,
        width: '100vh',
        height: '100vw',
        transformOrigin: 'center center'
      };
    case 'horizontal':
      return {
        ...baseStyles,
        transform: `rotate(${rotation}deg)`,
        width: '100vw',
        height: '100vh',
        transformOrigin: 'center center'
      };
    case 'auto':
    default:
      return {
        ...baseStyles,
        transform: `rotate(${rotation}deg)`,
        transformOrigin: 'center center'
      };
  }
};

// Função para calcular dimensões baseadas na rotação
export const getRotatedDimensions = (width, height, rotation) => {
  const angle = (rotation * Math.PI) / 180;
  const cos = Math.abs(Math.cos(angle));
  const sin = Math.abs(Math.sin(angle));
  
  return {
    width: width * cos + height * sin,
    height: width * sin + height * cos
  };
};

// Função para validar rotação
export const isValidRotation = (rotation) => {
  return ROTATION_OPTIONS.some(option => option.value === rotation);
};

// Função para obter próxima rotação
export const getNextRotation = (currentRotation) => {
  const currentIndex = ROTATION_OPTIONS.findIndex(option => option.value === currentRotation);
  const nextIndex = (currentIndex + 1) % ROTATION_OPTIONS.length;
  return ROTATION_OPTIONS[nextIndex].value;
};

// Função para obter rotação anterior
export const getPreviousRotation = (currentRotation) => {
  const currentIndex = ROTATION_OPTIONS.findIndex(option => option.value === currentRotation);
  const prevIndex = currentIndex === 0 ? ROTATION_OPTIONS.length - 1 : currentIndex - 1;
  return ROTATION_OPTIONS[prevIndex].value;
};
