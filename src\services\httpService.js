import axios from 'axios';
import env from '../config/env.js';

const API_URL = env.REACT_APP_API_URL;

/**
 * Serviço para gerenciar as requisições HTTP ao servidor com fallback para armazenamento local
 */
const api = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Interceptor para tratamento de erros
api.interceptors.response.use(
  response => response,
  error => {
    console.error('❌ Erro na requisição HTTP:', error);

    // Verificar se é erro de conexão
    if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
      console.error('🚫 Servidor MongoDB inacessível. Verifique se o servidor está rodando na porta 3001');
    }

    return Promise.reject(error);
  }
);

export const httpService = {
  /**
   * Salva uma mídia no servidor
   * @param {Object} media - Objeto de mídia
   * @returns {Promise<Object>} - <PERSON><PERSON><PERSON> salva
   */
  saveMedia: async (media) => {
    try {
      console.log('🗄️ Salvando mídia no banco de dados:', media.nome);

      const formData = new FormData();

      // Verificar se o arquivo existe na estrutura correta
      const arquivo = media.arquivo || media.file || media;
      let fileToUpload;

      console.log('Processando arquivo para upload:', {
        mediaType: media.type,
        arquivoType: typeof arquivo,
        isFile: arquivo instanceof File,
        hasFile: arquivo?.file instanceof File
      });

      if (arquivo instanceof File) {
        fileToUpload = arquivo;
        console.log('Usando arquivo File diretamente');
      } else if (arquivo && arquivo.file instanceof File) {
        fileToUpload = arquivo.file;
        console.log('Usando arquivo.file');
      } else if (arquivo && arquivo.preview && arquivo.type) {
        // Para vídeos, o preview é base64, precisamos converter
        try {
          console.log('Convertendo preview para arquivo...');
          const response = await fetch(arquivo.preview);
          const blob = await response.blob();
          fileToUpload = new File([blob], media.nome || 'arquivo', { type: arquivo.type });
          console.log('Conversão bem-sucedida');
        } catch (previewError) {
          console.error('Erro ao converter preview para arquivo:', previewError);
          throw previewError;
        }
      } else if (media.preview && media.type) {
        // Tentar usar o preview da mídia principal
        try {
          console.log('Convertendo preview da mídia para arquivo...');
          const response = await fetch(media.preview);
          const blob = await response.blob();
          fileToUpload = new File([blob], media.nome || 'arquivo', { type: media.type });
          console.log('Conversão bem-sucedida');
        } catch (previewError) {
          console.error('Erro ao converter preview da mídia para arquivo:', previewError);
          throw previewError;
        }
      }

      if (!fileToUpload) {
        throw new Error('Arquivo não encontrado na mídia ou formato inválido');
      }

      formData.append('file', fileToUpload);
      formData.append('mediaData', JSON.stringify({
        nome: media.nome,
        type: media.type || arquivo.type || fileToUpload.type,
        lastModified: media.lastModified || arquivo.lastModified || fileToUpload.lastModified,
        size: media.size || arquivo.size || fileToUpload.size,
        duracao: media.duracao || 0,
        // Incluir configurações de rotação
        rotation: media.rotation || 0,
        orientation: media.orientation || 'auto',
        fitMode: media.fitMode || 'cover',
        duration: media.duration || 10
      }));

      const response = await api.post('/medias', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      console.log('✅ Mídia salva no banco de dados com sucesso');
      return response.data;
    } catch (error) {
      console.error('❌ Erro ao salvar mídia no banco de dados:', error);
      throw error;
    }
  },

  /**
   * Carrega todas as mídias do servidor
   * @returns {Promise<Array>} - Array de mídias
   */
  loadMedias: async () => {
    try {
      console.log('🗄️ Carregando mídias do banco de dados...');
      const response = await api.get('/medias');
      console.log(`✅ ${response.data.length} mídias carregadas do banco de dados`);
      return response.data;
    } catch (error) {
      console.error('❌ Erro ao carregar mídias do banco de dados:', error);
      throw error;
    }
  },

  /**
   * Remove uma mídia do servidor
   * @param {string} id - ID da mídia
   */
  removeMedia: async (id) => {
    try {
      await api.delete(`/medias/${id}`);
    } catch (error) {
      console.error('Erro ao remover mídia:', error);
      throw error;
    }
  },

  /**
   * Remove uma mídia do servidor (alias para removeMedia)
   * @param {string} id - ID da mídia
   */
  deleteMedia: async (id) => {
    return httpService.removeMedia(id);
  },

  /**
   * Atualiza uma mídia no servidor
   * @param {string} id - ID da mídia
   * @param {Object} media - Dados atualizados da mídia
   * @returns {Promise<Object>} - Mídia atualizada
   */
  updateMedia: async (id, media) => {
    try {
      const response = await api.put(`/medias/${id}`, media);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar mídia:', error);
      throw error;
    }
  },

  /**
   * Salva uma playlist no servidor
   * @param {Object} playlist - Objeto da playlist
   * @returns {Promise<Object>} - Playlist salva
   */
  savePlaylist: async (playlist) => {
    try {
      console.log('🗄️ Salvando playlist no banco de dados:', playlist.nome);
      console.log('🔧 Configurações da playlist:', {
        orientation: playlist.orientation,
        autoRotate: playlist.autoRotate,
        transitionDuration: playlist.transitionDuration,
        adaptToContent: playlist.adaptToContent,
        globalRotation: playlist.globalRotation
      });

      const response = await api.post('/playlists', playlist);
      console.log('✅ Playlist salva no banco de dados com sucesso');
      return response.data;
    } catch (error) {
      console.error('❌ Erro ao salvar playlist no banco de dados:', error);
      throw error;
    }
  },

  /**
   * Carrega todas as playlists do servidor
   * @returns {Promise<Array>} - Array de playlists
   */
  loadPlaylists: async () => {
    try {
      console.log('🗄️ Carregando playlists do banco de dados...');
      const response = await api.get('/playlists');
      console.log(`✅ ${response.data.length} playlists carregadas do banco de dados`);
      return response.data;
    } catch (error) {
      console.error('❌ Erro ao carregar playlists do banco de dados:', error);
      throw error;
    }
  },

  /**
   * Atualiza uma playlist no servidor
   * @param {string} id - ID da playlist
   * @param {Object} playlist - Dados atualizados da playlist
   * @returns {Promise<Object>} - Playlist atualizada
   */
  updatePlaylist: async (id, playlist) => {
    try {
      console.log('🗄️ Atualizando playlist no banco de dados:', playlist.nome);
      console.log('🔧 Configurações da playlist sendo atualizadas:', {
        orientation: playlist.orientation,
        autoRotate: playlist.autoRotate,
        transitionDuration: playlist.transitionDuration,
        adaptToContent: playlist.adaptToContent,
        globalRotation: playlist.globalRotation
      });

      const response = await api.put(`/playlists/${id}`, playlist);
      console.log('✅ Playlist atualizada no banco de dados com sucesso');
      return response.data;
    } catch (error) {
      console.error('❌ Erro ao atualizar playlist no banco de dados:', error);
      throw error;
    }
  },

  /**
   * Remove uma playlist do servidor
   * @param {string} id - ID da playlist
   */
  removePlaylist: async (id) => {
    try {
      await api.delete(`/playlists/${id}`);
    } catch (error) {
      console.error('Erro ao remover playlist:', error);
      throw error;
    }
  },

  /**
   * Remove uma playlist do servidor (alias para removePlaylist)
   * @param {string} id - ID da playlist
   */
  deletePlaylist: async (id) => {
    return httpService.removePlaylist(id);
  },

  /**
   * Salva uma tela no servidor
   * @param {Object} tela - Objeto de tela
   * @returns {Promise<Object>} - Tela salva
   */
  saveTela: async (tela) => {
    try {
      const response = await api.post('/telas', tela);
      return response.data;
    } catch (error) {
      console.error('Erro ao salvar tela:', error);

      // Em caso de erro de conexão, salvar localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const telas = JSON.parse(localStorage.getItem('telas') || '[]');
        const novaTela = { ...tela, id: tela.id || Date.now() };
        telas.push(novaTela);
        localStorage.setItem('telas', JSON.stringify(telas));
        return novaTela;
      }

      throw error;
    }
  },

  /**
   * Atualiza uma tela no servidor
   * @param {string} id - ID da tela
   * @param {Object} tela - Dados atualizados da tela
   * @returns {Promise<Object>} - Tela atualizada
   */
  updateTela: async (id, tela) => {
    try {
      const response = await api.put(`/telas/${id}`, tela);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar tela:', error);

      // Em caso de erro de conexão, atualizar localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const telas = JSON.parse(localStorage.getItem('telas') || '[]');
        const updatedTelas = telas.map(t => t.id === id ? { ...t, ...tela } : t);
        localStorage.setItem('telas', JSON.stringify(updatedTelas));
        return { ...tela, id };
      }

      throw error;
    }
  },

  /**
   * Carrega todas as telas do servidor
   * @returns {Promise<Array>} - Array de telas
   */
  loadTelas: async () => {
    try {
      const response = await api.get('/telas');
      return response.data;
    } catch (error) {
      console.error('Erro ao carregar telas:', error);
      // Em caso de erro, retornar dados do localStorage
      return JSON.parse(localStorage.getItem('telas') || '[]');
    }
  },

  /**
   * Remove uma tela do servidor
   * @param {string} id - ID da tela
   */
  removeTela: async (id) => {
    try {
      await api.delete(`/telas/${id}`);
    } catch (error) {
      console.error('Erro ao remover tela:', error);

      // Em caso de erro de conexão, remover localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const telas = JSON.parse(localStorage.getItem('telas') || '[]');
        const updatedTelas = telas.filter(t => t.id !== id);
        localStorage.setItem('telas', JSON.stringify(updatedTelas));
        return;
      }

      throw error;
    }
  }
};

export default httpService;