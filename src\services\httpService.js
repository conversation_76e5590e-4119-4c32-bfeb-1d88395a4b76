import axios from 'axios';
import env from '../config/env.js';

const API_URL = env.REACT_APP_API_URL;

/**
 * Serviço para gerenciar as requisições HTTP ao servidor com fallback para armazenamento local
 */
const api = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Interceptor para tratamento de erros
api.interceptors.response.use(
  response => response,
  error => {
    console.error('Erro na requisição HTTP:', error);

    // Se o servidor estiver inacessível, salvar localmente
    if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
      console.log('Servidor inacessível, salvando dados localmente...');
      return handleOfflineStorage(error.config);
    }

    return Promise.reject(error);
  }
);

// Manipulador para armazenamento offline
const handleOfflineStorage = async (config) => {
  try {
    const { method, url, data } = config;
    const storageKey = url.includes('/playlists') ? 'playlists' : 'midias';

    // Carregar dados existentes
    let items = JSON.parse(localStorage.getItem(storageKey) || '[]');

    if (method === 'PUT') {
      const itemId = url.split('/').pop();
      const updatedItem = JSON.parse(data);

      items = items.map(item =>
        item.id === parseInt(itemId) ? updatedItem : item
      );
    }
    else if (method === 'POST') {
      const newItem = JSON.parse(data);
      items.push(newItem);
    }
    else if (method === 'DELETE') {
      const itemId = url.split('/').pop();
      items = items.filter(item => item.id !== parseInt(itemId));
    }

    // Salvar no localStorage
    localStorage.setItem(storageKey, JSON.stringify(items));

    return { data: items };
  } catch (error) {
    console.error('Erro ao salvar offline:', error);
    throw error;
  }
};

export const httpService = {
  /**
   * Salva uma mídia no servidor
   * @param {Object} media - Objeto de mídia
   * @returns {Promise<Object>} - Mídia salva
   */
  saveMedia: async (media) => {
    try {
      const formData = new FormData();

      // Verificar se o arquivo existe na estrutura correta
      const arquivo = media.arquivo || media.file || media;
      let fileToUpload;

      console.log('Processando arquivo para upload:', {
        mediaType: media.type,
        arquivoType: typeof arquivo,
        isFile: arquivo instanceof File,
        hasFile: arquivo?.file instanceof File
      });

      if (arquivo instanceof File) {
        fileToUpload = arquivo;
        console.log('Usando arquivo File diretamente');
      } else if (arquivo && arquivo.file instanceof File) {
        fileToUpload = arquivo.file;
        console.log('Usando arquivo.file');
      } else if (arquivo && arquivo.preview && arquivo.type) {
        // Para vídeos, o preview é base64, precisamos converter
        try {
          console.log('Convertendo preview para arquivo...');
          const response = await fetch(arquivo.preview);
          const blob = await response.blob();
          fileToUpload = new File([blob], media.nome || 'arquivo', { type: arquivo.type });
          console.log('Conversão bem-sucedida');
        } catch (previewError) {
          console.error('Erro ao converter preview para arquivo:', previewError);
        }
      } else if (media.preview && media.type) {
        // Tentar usar o preview da mídia principal
        try {
          console.log('Convertendo preview da mídia para arquivo...');
          const response = await fetch(media.preview);
          const blob = await response.blob();
          fileToUpload = new File([blob], media.nome || 'arquivo', { type: media.type });
          console.log('Conversão bem-sucedida');
        } catch (previewError) {
          console.error('Erro ao converter preview da mídia para arquivo:', previewError);
        }
      }

      if (!fileToUpload) {
        throw new Error('Arquivo não encontrado na mídia ou formato inválido');
      }

      formData.append('file', fileToUpload);
      formData.append('mediaData', JSON.stringify({
        nome: media.nome,
        type: media.type || arquivo.type || fileToUpload.type,
        lastModified: media.lastModified || arquivo.lastModified || fileToUpload.lastModified,
        size: media.size || arquivo.size || fileToUpload.size,
        duracao: media.duracao || 0 // Adicionar duração para vídeos
      }));

      const response = await api.post('/medias', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao salvar mídia:', error);

      // Em caso de erro de conexão, salvar localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const midias = JSON.parse(localStorage.getItem('midias') || '[]');
        const novaMidia = {
          ...media,
          id: media.id || Date.now()
        };
        midias.push(novaMidia);
        localStorage.setItem('midias', JSON.stringify(midias));
        return novaMidia;
      }

      throw error;
    }
  },

  /**
   * Carrega todas as mídias do servidor
   * @returns {Promise<Array>} - Array de mídias
   */
  loadMedias: async () => {
    try {
      const response = await api.get('/medias');
      return response.data;
    } catch (error) {
      console.error('Erro ao carregar mídias:', error);
      // Em caso de erro, retornar dados do localStorage
      return JSON.parse(localStorage.getItem('midias') || '[]');
    }
  },

  /**
   * Remove uma mídia do servidor
   * @param {string} id - ID da mídia
   */
  removeMedia: async (id) => {
    try {
      await api.delete(`/medias/${id}`);
    } catch (error) {
      console.error('Erro ao remover mídia:', error);

      // Em caso de erro de conexão, remover localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const midias = JSON.parse(localStorage.getItem('midias') || '[]');
        const updatedMidias = midias.filter(m => m.id !== id);
        localStorage.setItem('midias', JSON.stringify(updatedMidias));
        return;
      }

      throw error;
    }
  },

  /**
   * Salva uma playlist no servidor
   * @param {Object} playlist - Objeto da playlist
   * @returns {Promise<Object>} - Playlist salva
   */
  savePlaylist: async (playlist) => {
    try {
      const response = await api.post('/playlists', playlist);
      return response.data;
    } catch (error) {
      console.error('Erro ao salvar playlist:', error);

      // Em caso de erro de conexão, salvar localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const playlists = JSON.parse(localStorage.getItem('playlists') || '[]');
        const novaPlaylist = {
          ...playlist,
          id: Date.now()
        };
        playlists.push(novaPlaylist);
        localStorage.setItem('playlists', JSON.stringify(playlists));
        return novaPlaylist;
      }

      throw error;
    }
  },

  /**
   * Carrega todas as playlists do servidor
   * @returns {Promise<Array>} - Array de playlists
   */
  loadPlaylists: async () => {
    try {
      const response = await api.get('/playlists');
      return response.data;
    } catch (error) {
      console.error('Erro ao carregar playlists:', error);
      // Em caso de erro, retornar dados do localStorage
      return JSON.parse(localStorage.getItem('playlists') || '[]');
    }
  },

  /**
   * Atualiza uma playlist no servidor
   * @param {string} id - ID da playlist
   * @param {Object} playlist - Dados atualizados da playlist
   * @returns {Promise<Object>} - Playlist atualizada
   */
  updatePlaylist: async (id, playlist) => {
    try {
      const response = await api.put(`/playlists/${id}`, playlist);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar playlist:', error);

      // Em caso de erro de conexão, atualizar localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const playlists = JSON.parse(localStorage.getItem('playlists') || '[]');
        const updatedPlaylists = playlists.map(p => p.id === id ? playlist : p);
        localStorage.setItem('playlists', JSON.stringify(updatedPlaylists));
        return playlist;
      }

      throw error;
    }
  },

  /**
   * Remove uma playlist do servidor
   * @param {string} id - ID da playlist
   */
  removePlaylist: async (id) => {
    try {
      await api.delete(`/playlists/${id}`);
    } catch (error) {
      console.error('Erro ao remover playlist:', error);

      // Em caso de erro de conexão, remover localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const playlists = JSON.parse(localStorage.getItem('playlists') || '[]');
        const updatedPlaylists = playlists.filter(p => p.id !== id);
        localStorage.setItem('playlists', JSON.stringify(updatedPlaylists));
        return;
      }

      throw error;
    }
  },

  /**
   * Salva uma tela no servidor
   * @param {Object} tela - Objeto de tela
   * @returns {Promise<Object>} - Tela salva
   */
  saveTela: async (tela) => {
    try {
      const response = await api.post('/telas', tela);
      return response.data;
    } catch (error) {
      console.error('Erro ao salvar tela:', error);

      // Em caso de erro de conexão, salvar localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const telas = JSON.parse(localStorage.getItem('telas') || '[]');
        const novaTela = { ...tela, id: tela.id || Date.now() };
        telas.push(novaTela);
        localStorage.setItem('telas', JSON.stringify(telas));
        return novaTela;
      }

      throw error;
    }
  },

  /**
   * Atualiza uma tela no servidor
   * @param {string} id - ID da tela
   * @param {Object} tela - Dados atualizados da tela
   * @returns {Promise<Object>} - Tela atualizada
   */
  updateTela: async (id, tela) => {
    try {
      const response = await api.put(`/telas/${id}`, tela);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar tela:', error);

      // Em caso de erro de conexão, atualizar localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const telas = JSON.parse(localStorage.getItem('telas') || '[]');
        const updatedTelas = telas.map(t => t.id === id ? { ...t, ...tela } : t);
        localStorage.setItem('telas', JSON.stringify(updatedTelas));
        return { ...tela, id };
      }

      throw error;
    }
  },

  /**
   * Carrega todas as telas do servidor
   * @returns {Promise<Array>} - Array de telas
   */
  loadTelas: async () => {
    try {
      const response = await api.get('/telas');
      return response.data;
    } catch (error) {
      console.error('Erro ao carregar telas:', error);
      // Em caso de erro, retornar dados do localStorage
      return JSON.parse(localStorage.getItem('telas') || '[]');
    }
  },

  /**
   * Remove uma tela do servidor
   * @param {string} id - ID da tela
   */
  removeTela: async (id) => {
    try {
      await api.delete(`/telas/${id}`);
    } catch (error) {
      console.error('Erro ao remover tela:', error);

      // Em caso de erro de conexão, remover localmente
      if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
        const telas = JSON.parse(localStorage.getItem('telas') || '[]');
        const updatedTelas = telas.filter(t => t.id !== id);
        localStorage.setItem('telas', JSON.stringify(updatedTelas));
        return;
      }

      throw error;
    }
  }
};

export default httpService;