/**
 * Utilitários para migração de dados do localStorage para o banco de dados
 */

import httpService from '../services/httpService';

/**
 * Migra telas do localStorage para o banco de dados
 * @returns {Promise<Object>} - Resultado da migração
 */
export const migrateTelasToDatabase = async () => {
  try {
    console.log('🔄 Iniciando migração de telas do localStorage para o banco...');
    
    // Verificar se há telas no localStorage
    const localTelas = localStorage.getItem('telas');
    if (!localTelas) {
      console.log('✅ Nenhuma tela encontrada no localStorage para migrar.');
      return { success: true, migrated: 0, message: 'Nenhuma tela para migrar' };
    }

    const telas = JSON.parse(localTelas);
    if (!Array.isArray(telas) || telas.length === 0) {
      console.log('✅ Nenhuma tela válida encontrada no localStorage.');
      return { success: true, migrated: 0, message: 'Nenhuma tela válida para migrar' };
    }

    console.log(`📊 Encontradas ${telas.length} telas no localStorage`);

    // Verificar telas existentes no banco
    const existingTelas = await httpService.loadTelas();
    const existingIds = new Set(existingTelas.map(t => t.id));

    // Filtrar telas que ainda não existem no banco
    const telasToMigrate = telas.filter(tela => !existingIds.has(tela.id));
    
    if (telasToMigrate.length === 0) {
      console.log('✅ Todas as telas já existem no banco de dados.');
      return { success: true, migrated: 0, message: 'Todas as telas já migradas' };
    }

    console.log(`🚀 Migrando ${telasToMigrate.length} telas para o banco...`);

    // Migrar cada tela
    let migratedCount = 0;
    const errors = [];

    for (const tela of telasToMigrate) {
      try {
        await httpService.saveTela(tela);
        migratedCount++;
        console.log(`✅ Tela "${tela.nome}" migrada com sucesso`);
      } catch (error) {
        console.error(`❌ Erro ao migrar tela "${tela.nome}":`, error);
        errors.push({ tela: tela.nome, error: error.message });
      }
    }

    // Criar backup do localStorage antes de limpar
    const backupKey = `telas_backup_${Date.now()}`;
    localStorage.setItem(backupKey, localTelas);
    console.log(`💾 Backup criado em: ${backupKey}`);

    // Limpar localStorage após migração bem-sucedida
    if (migratedCount > 0 && errors.length === 0) {
      localStorage.removeItem('telas');
      console.log('🧹 localStorage limpo após migração bem-sucedida');
    }

    const result = {
      success: true,
      migrated: migratedCount,
      errors: errors.length,
      message: `${migratedCount} telas migradas com sucesso${errors.length > 0 ? `, ${errors.length} erros` : ''}`,
      errorDetails: errors
    };

    console.log('🎉 Migração concluída:', result);
    return result;

  } catch (error) {
    console.error('❌ Erro durante a migração:', error);
    return {
      success: false,
      migrated: 0,
      errors: 1,
      message: 'Erro durante a migração',
      errorDetails: [{ error: error.message }]
    };
  }
};

/**
 * Verifica se há dados no localStorage que precisam ser migrados
 * @returns {Object} - Status da migração necessária
 */
export const checkMigrationNeeded = () => {
  const localTelas = localStorage.getItem('telas');
  
  if (!localTelas) {
    return { needed: false, count: 0 };
  }

  try {
    const telas = JSON.parse(localTelas);
    const count = Array.isArray(telas) ? telas.length : 0;
    
    return {
      needed: count > 0,
      count,
      message: count > 0 ? `${count} telas encontradas no localStorage` : 'Nenhuma tela no localStorage'
    };
  } catch (error) {
    console.error('Erro ao verificar dados do localStorage:', error);
    return { needed: false, count: 0, error: error.message };
  }
};

/**
 * Limpa backups antigos do localStorage (mais de 7 dias)
 */
export const cleanupOldBackups = () => {
  try {
    const keys = Object.keys(localStorage);
    const backupKeys = keys.filter(key => key.startsWith('telas_backup_'));
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

    let cleaned = 0;
    backupKeys.forEach(key => {
      const timestamp = parseInt(key.replace('telas_backup_', ''));
      if (timestamp < sevenDaysAgo) {
        localStorage.removeItem(key);
        cleaned++;
      }
    });

    if (cleaned > 0) {
      console.log(`🧹 ${cleaned} backups antigos removidos`);
    }

    return { cleaned };
  } catch (error) {
    console.error('Erro ao limpar backups:', error);
    return { cleaned: 0, error: error.message };
  }
};
