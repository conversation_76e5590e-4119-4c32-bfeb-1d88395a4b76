import React, { useState } from 'react';
import { <PERSON>, Container, TextField, Button, Typography, Alert, Avatar } from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

const validationSchema = yup.object({
  nome: yup
    .string()
    .min(3, 'Nome deve ter no mínimo 3 caracteres')
    .required('Nome é obrigatório'),
  cnpj: yup
    .string()
    .matches(/^\d{14}$/, 'CNPJ deve conter 14 dígitos')
    .required('CNPJ é obrigatório'),
  email: yup
    .string()
    .email('Digite um e-mail válido')
    .required('E-mail é obrigatório'),
});

const CadastroEmpresa = () => {
  const [logo, setLogo] = useState(null);
  const [previewLogo, setPreviewLogo] = useState(null);
  const [status, setStatus] = useState({ type: '', message: '' });

  const handleLogoChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setLogo(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewLogo(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const formik = useFormik({
    initialValues: {
      nome: '',
      cnpj: '',
      email: '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        // Implementar lógica de cadastro aqui
        const formData = new FormData();
        formData.append('nome', values.nome);
        formData.append('cnpj', values.cnpj);
        formData.append('email', values.email);
        if (logo) {
          formData.append('logo', logo);
        }

        console.log('Form values:', Object.fromEntries(formData));
        setStatus({
          type: 'success',
          message: 'Empresa cadastrada com sucesso!',
        });
      } catch (error) {
        setStatus({
          type: 'error',
          message: 'Erro ao cadastrar empresa. Tente novamente.',
        });
      }
    },
  });

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          bgcolor: 'white',
          p: 4,
          borderRadius: 2,
          boxShadow: '0 3px 5px 2px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Typography component="h1" variant="h5" sx={{ mb: 3, color: '#333' }}>
          Cadastro de Empresa
        </Typography>

        {status.type && (
          <Alert severity={status.type} sx={{ width: '100%', mb: 2 }}>
            {status.message}
          </Alert>
        )}

        <Box component="form" onSubmit={formik.handleSubmit} sx={{ mt: 1, width: '100%' }}>
          <Box sx={{ mb: 3, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <Avatar
              src={previewLogo}
              sx={{
                width: 100,
                height: 100,
                mb: 2,
                bgcolor: previewLogo ? 'transparent' : '#e0e0e0',
              }}
            >
              {!previewLogo && <CloudUploadIcon />}
            </Avatar>
            <Button
              component="label"
              variant="outlined"
              sx={{
                color: '#007BFF',
                borderColor: '#007BFF',
                '&:hover': {
                  borderColor: '#0056b3',
                  bgcolor: 'rgba(0, 123, 255, 0.1)',
                },
              }}
            >
              Upload Logo
              <input
                type="file"
                hidden
                accept="image/*"
                onChange={handleLogoChange}
              />
            </Button>
          </Box>

          <TextField
            margin="normal"
            required
            fullWidth
            id="nome"
            label="Nome da Empresa"
            name="nome"
            autoFocus
            value={formik.values.nome}
            onChange={formik.handleChange}
            error={formik.touched.nome && Boolean(formik.errors.nome)}
            helperText={formik.touched.nome && formik.errors.nome}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="normal"
            required
            fullWidth
            id="cnpj"
            label="CNPJ"
            name="cnpj"
            value={formik.values.cnpj}
            onChange={formik.handleChange}
            error={formik.touched.cnpj && Boolean(formik.errors.cnpj)}
            helperText={formik.touched.cnpj && formik.errors.cnpj}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="E-mail"
            name="email"
            autoComplete="email"
            value={formik.values.email}
            onChange={formik.handleChange}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            sx={{ mb: 3 }}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{
              mt: 3,
              mb: 2,
              bgcolor: '#007BFF',
              '&:hover': {
                bgcolor: '#0056b3',
              },
              height: '48px',
              fontSize: '1rem',
            }}
          >
            Cadastrar Empresa
          </Button>

          <Button
            fullWidth
            variant="text"
            href="/login"
            sx={{
              color: '#007BFF',
              textDecoration: 'none',
              '&:hover': {
                bgcolor: 'rgba(0, 123, 255, 0.1)',
              },
            }}
          >
            Voltar para o Login
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default CadastroEmpresa;