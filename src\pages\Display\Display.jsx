import React, { useState, useEffect, useRef } from 'react';
import { Box, CircularProgress, Typography, Fade, LinearProgress, IconButton, Button, Snackbar, Alert } from '@mui/material';
import { useParams } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import InfoIcon from '@mui/icons-material/Info';
import { loadPlaylistsFromStorage } from '../../utils/storageUtils';
import { usePlaylistRotation } from '../../hooks/useRotation';
import { getOrientationCSS } from '../../constants/rotation';

// Componente estilizado para o container principal
const DisplayContainer = styled(Box)(({ theme, rotation = 0, orientation = 'auto' }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  width: '100vw',
  height: '100vh',
  overflow: 'hidden',
  backgroundColor: '#000',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  userSelect: 'none',
  zIndex: 1000,
  transform: `rotate(${rotation}deg)`,
  transformOrigin: 'center center',
  transition: 'transform 0.5s ease-in-out',

  '@media (max-width: 768px)': {
    padding: 0,
  },

  '@media (orientation: portrait)': {
    flexDirection: orientation === 'vertical' ? 'column' : 'row',
  },

  '@media (orientation: landscape)': {
    flexDirection: orientation === 'horizontal' ? 'row' : 'column',
  },
}));

// Componente estilizado para o conteúdo de mídia
const MediaContent = styled(Box)(({ theme, orientation, mediaRotation = 0, fitMode = 'contain', transitionDuration = 500 }) => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  position: 'absolute',
  top: 0,
  left: 0,
  transition: `opacity ${transitionDuration}ms cubic-bezier(0.4, 0, 0.2, 1), transform 0.5s ease-in-out`,

  '& img, & video': {
    maxWidth: '100%',
    maxHeight: '100%',
    width: 'auto',
    height: 'auto',
    objectFit: fitMode,
    transform: `rotate(${mediaRotation}deg)`,
    transformOrigin: 'center center',
    transition: `transform ${transitionDuration}ms ease-in-out, object-fit 0.3s ease`,

    '@media (max-width: 768px)': {
      width: '100vw',
      height: '100vh',
      objectFit: fitMode,
    },

    '@media (orientation: portrait)': {
      width: '100%',
      height: 'auto',
      maxHeight: '100vh',
    },

    '@media (orientation: landscape)': {
      width: 'auto',
      height: '100%',
      maxWidth: '100vw',
    },
  },
}));

// Componente estilizado para o overlay de informações
const InfoOverlay = styled(Box)(({ theme, visible }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  padding: theme.spacing(2),
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  color: '#fff',
  transform: `translateY(${visible ? '0' : '100%'})`,
  transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  zIndex: 10,
  backdropFilter: 'blur(5px)',
  borderTop: '1px solid rgba(255, 255, 255, 0.1)',
}));

// Componente estilizado para o indicador de carregamento
const LoadingContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  backgroundColor: '#000',
  color: '#fff',
  zIndex: 20,
}));

// Componente estilizado para os controles
const ControlsOverlay = styled(Box)(({ theme, visible }) => ({
  position: 'absolute',
  top: 0,
  right: 0,
  padding: theme.spacing(2),
  color: '#fff',
  opacity: visible ? 1 : 0,
  transition: 'opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  zIndex: 10,
  display: 'flex',
  gap: theme.spacing(1),
}));

// Componente estilizado para o indicador de progresso
const ProgressBar = styled(LinearProgress)(({ theme }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  height: 4,
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  '& .MuiLinearProgress-bar': {
    backgroundColor: theme.palette.primary.main,
  },
  zIndex: 9,
}));

const Display = () => {
  const { id } = useParams();
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);
  const [previousMediaIndex, setPreviousMediaIndex] = useState(null);
  const [playlist, setPlaylist] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isAutoplay, setIsAutoplay] = useState(true);
  const [orientation, setOrientation] = useState('horizontal');
  const [showInfo, setShowInfo] = useState(false);
  const [showControls, setShowControls] = useState(false);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);
  const videoRef = useRef(null);
  const progressTimerRef = useRef(null);

  // Estados para rotação e orientação
  const [displayRotation, setDisplayRotation] = useState(0);
  const [displayOrientation, setDisplayOrientation] = useState('auto');

  // Estados para configurações da playlist
  const [playlistConfig, setPlaylistConfig] = useState({
    orientation: 'mixed',
    autoRotate: false,
    transitionDuration: 500,
    adaptToContent: true,
    globalRotation: 0
  });

  // Hook para gerenciar rotação da playlist
  const playlistRotation = usePlaylistRotation({
    orientation: playlistConfig.orientation,
    globalRotation: playlistConfig.globalRotation || displayRotation,
    autoRotate: playlistConfig.autoRotate,
    adaptToContent: playlistConfig.adaptToContent,
    transitionDuration: playlistConfig.transitionDuration
  });

  // Detectar orientação da tela
  useEffect(() => {
    const updateOrientation = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'vertical' : 'horizontal');
    };

    updateOrientation();
    window.addEventListener('resize', updateOrientation);

    return () => {
      window.removeEventListener('resize', updateOrientation);
    };
  }, []);

  // Cache da playlist atual
  const [cachedPlaylist, setCachedPlaylist] = useState(null);

  // Carregar playlist
  useEffect(() => {
    const fetchPlaylist = async () => {
      // Remover tela de carregamento - carregar em background

      try {
        // Sempre buscar playlists atualizadas do storage
        const playlists = await loadPlaylistsFromStorage();

        // Buscar playlist pelo nome ou pelo ID
        let selectedPlaylist = playlists.find(p => p.nome === id);

        // Se não encontrar pelo nome, tenta pelo ID (para compatibilidade)
        if (!selectedPlaylist) {
          selectedPlaylist = playlists.find(p => p.id === parseInt(id));
        }

        console.log('Buscando playlist:', id);
        console.log('Playlists disponíveis:', playlists);
        console.log('Playlist selecionada:', selectedPlaylist);

        if (!selectedPlaylist) {
          setError(`Playlist "${id}" não encontrada. Verifique se a playlist existe.`);
          setIsLoading(false);
          return;
        }

        if (!selectedPlaylist.midias || selectedPlaylist.midias.length === 0) {
          setError(`A playlist "${selectedPlaylist.nome}" está vazia. Adicione mídias à playlist.`);
          setIsLoading(false);
          return;
        }

        // Verificar se a playlist mudou
        const playlistChanged = !cachedPlaylist ||
          cachedPlaylist.id !== selectedPlaylist.id ||
          JSON.stringify(selectedPlaylist.midias) !== JSON.stringify(cachedPlaylist.midias);

        // Se a playlist não mudou, manter o estado atual
        if (!playlistChanged) {
          setIsLoading(false);
          return;
        }

        // Resetar estados apenas se a playlist mudou
        setCurrentMediaIndex(0);
        setPreviousMediaIndex(null);
        setProgress(0);

        // Formatar a playlist para o formato esperado pelo display
        const formattedPlaylist = {
          id: selectedPlaylist.id,
          nome: selectedPlaylist.nome,
          medias: selectedPlaylist.midias.map(midia => {
            // Determinar o tipo de mídia de forma mais robusta
            let tipo = 'imagem'; // Padrão para imagem

            // Verificar tipo diretamente no objeto midia
            if (midia.type && midia.type.startsWith('video/')) {
              tipo = 'video';
            }
            // Verificar no objeto arquivo se existir
            else if (midia.arquivo && midia.arquivo.type && midia.arquivo.type.startsWith('video/')) {
              tipo = 'video';
            }

            // Determinar a URL da mídia
            let url = '';
            if (midia.originalUrl) {
              url = midia.originalUrl;
            } else if (midia.preview) {
              url = midia.preview;
            } else if (midia.arquivo && midia.arquivo.preview) {
              url = midia.arquivo.preview;
            } else if (midia.thumbnail) {
              url = midia.thumbnail;
            }

            // Determinar a duração
            let duracao = 10; // Padrão para imagens
            if (tipo === 'video') {
              duracao = midia.duracao || midia.duration || 0;
            } else {
              duracao = midia.duracao || midia.duration || 10;
            }

            return {
              id: midia.id,
              nome: midia.nome,
              tipo: tipo,
              url: url,
              duracao: duracao
            };
          })
        };


        // Verificar e ajustar durações dos vídeos
        for (let i = 0; i < formattedPlaylist.medias.length; i++) {
          const media = formattedPlaylist.medias[i];
          if (media.tipo === 'video' && media.duracao === 0) {
            const video = document.createElement('video');
            video.crossOrigin = "anonymous";
            video.preload = "metadata";
            video.src = media.url;
            await new Promise((resolve) => {
              video.onloadedmetadata = () => {
                media.duracao = Math.ceil(video.duration);
                console.log(`Duração do vídeo ${media.nome}: ${media.duracao}s`);
                resolve();
              };
              video.onerror = (e) => {
                console.error(`Erro ao carregar metadados do vídeo ${media.nome}:`, e);
                media.duracao = 10; // Duração padrão em caso de erro
                resolve();
              };
              // Adicionar timeout para evitar espera infinita
              setTimeout(() => {
                if (media.duracao === 0) {
                  console.warn(`Timeout ao carregar metadados do vídeo ${media.nome}`);
                  media.duracao = 10; // Duração padrão em caso de timeout
                  resolve();
                }
              }, 5000);
            });
          }
        }

        // Carregar configurações da playlist
        console.log('🎵 Carregando configurações da playlist:', {
          orientation: selectedPlaylist.orientation,
          autoRotate: selectedPlaylist.autoRotate,
          transitionDuration: selectedPlaylist.transitionDuration,
          adaptToContent: selectedPlaylist.adaptToContent,
          globalRotation: selectedPlaylist.globalRotation
        });

        setPlaylistConfig({
          orientation: selectedPlaylist.orientation || 'mixed',
          autoRotate: selectedPlaylist.autoRotate || false,
          transitionDuration: selectedPlaylist.transitionDuration || 500,
          adaptToContent: selectedPlaylist.adaptToContent !== undefined ? selectedPlaylist.adaptToContent : true,
          globalRotation: selectedPlaylist.globalRotation || 0
        });

        setPlaylist(formattedPlaylist);
        setCachedPlaylist(selectedPlaylist);
      } catch (error) {
        console.error('Erro ao carregar playlist:', error);
        setError('Não foi possível carregar a playlist. Tente novamente mais tarde.');
      }
    };

    fetchPlaylist();

    // Verificar parâmetros da URL para autoplay, rotação e orientação
    const urlParams = new URLSearchParams(window.location.search);

    const autoplayParam = urlParams.get('autoplay');
    if (autoplayParam !== null) {
      setIsAutoplay(autoplayParam === 'true');
    }

    const rotationParam = urlParams.get('rotation');
    if (rotationParam !== null) {
      const rotation = parseInt(rotationParam);
      if ([0, 90, 180, 270, 360].includes(rotation)) {
        setDisplayRotation(rotation);
      }
    }

    const orientationParam = urlParams.get('orientation');
    if (orientationParam !== null) {
      if (['horizontal', 'vertical', 'auto'].includes(orientationParam)) {
        setDisplayOrientation(orientationParam);
      }
    }

  }, [id]);

  // Controlar a transição entre mídias e atualizar a barra de progresso com sincronização global
  useEffect(() => {
    if (!playlist || !isAutoplay || isLoading) return;

    const currentMedia = playlist.medias[currentMediaIndex];
    const duration = currentMedia.duracao * 1000;

    // Calcular tempo de início baseado em timestamp global para sincronização
    const now = Date.now();
    const totalPlaylistDuration = playlist.medias.reduce((total, media) => total + (media.duracao * 1000), 0);
    const cycleStartTime = Math.floor(now / totalPlaylistDuration) * totalPlaylistDuration;

    let accumulatedTime = 0;
    let targetMediaIndex = 0;

    // Encontrar qual mídia deveria estar tocando agora
    for (let i = 0; i < playlist.medias.length; i++) {
      const mediaEndTime = accumulatedTime + (playlist.medias[i].duracao * 1000);
      if ((now - cycleStartTime) < mediaEndTime) {
        targetMediaIndex = i;
        break;
      }
      accumulatedTime += playlist.medias[i].duracao * 1000;
    }

    // Sincronizar com o índice correto se necessário
    if (targetMediaIndex !== currentMediaIndex) {
      setCurrentMediaIndex(targetMediaIndex);
      return;
    }

    const mediaStartTime = cycleStartTime + accumulatedTime;
    const timeInCurrentMedia = now - mediaStartTime;
    const remainingTime = duration - timeInCurrentMedia;

    let startTime;

    // Configurar o timer para a próxima mídia
    const timer = setTimeout(() => {
      setPreviousMediaIndex(currentMediaIndex);
      setCurrentMediaIndex((prevIndex) =>
        prevIndex === playlist.medias.length - 1 ? 0 : prevIndex + 1
      );
      setProgress(0);
    }, Math.max(remainingTime, 0));

    // Atualizar a barra de progresso
    const updateProgress = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime + timeInCurrentMedia;
      const progressValue = Math.min((elapsed / duration) * 100, 100);
      setProgress(progressValue);

      if (elapsed < duration) {
        progressTimerRef.current = requestAnimationFrame(updateProgress);
      }
    };

    progressTimerRef.current = requestAnimationFrame(updateProgress);

    return () => {
      clearTimeout(timer);
      if (progressTimerRef.current) {
        cancelAnimationFrame(progressTimerRef.current);
      }
    };
  }, [currentMediaIndex, playlist, isAutoplay, isLoading]);

  // Controlar a reprodução de vídeos quando se tornam ativos
  useEffect(() => {
    if (!playlist || isLoading || !videoRef.current) return;

    const currentMedia = playlist.medias[currentMediaIndex];
    if (currentMedia.tipo === 'video') {
      console.log('Iniciando reprodução do vídeo:', currentMedia.nome);

      // Garantir que o vídeo seja reproduzido
      const playVideo = async () => {
        try {
          // Recarregar o vídeo para garantir que ele seja reproduzido corretamente
          videoRef.current.load();
          const playPromise = videoRef.current.play();

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                console.log('Reprodução de vídeo iniciada com sucesso');
              })
              .catch(error => {
                console.error('Erro ao reproduzir vídeo:', error);
                // Tentar novamente após um curto intervalo
                setTimeout(() => {
                  if (videoRef.current) videoRef.current.play().catch(e => console.error('Falha na segunda tentativa:', e));
                }, 1000);
              });
          }
        } catch (error) {
          console.error('Erro ao manipular vídeo:', error);
        }
      };

      playVideo();
    }
  }, [currentMediaIndex, playlist, isLoading]);

  // Verificar atualizações da playlist periodicamente e sincronização global
  useEffect(() => {
    if (isLoading) return;

    const checkForUpdates = async () => {
      try {
        const playlists = await loadPlaylistsFromStorage();
        const updatedPlaylist = playlists.find(p => p.nome === id) || playlists.find(p => p.id === parseInt(id));

        if (!updatedPlaylist) {
          setError(`Playlist "${id}" não encontrada. Verifique se a playlist existe.`);
          return;
        }

        // Verificar se houve mudanças na playlist
        if (cachedPlaylist &&
            (cachedPlaylist.id !== updatedPlaylist.id ||
             JSON.stringify(updatedPlaylist.midias) !== JSON.stringify(cachedPlaylist.midias))) {
          console.log('Atualizações detectadas na playlist, recarregando...');
          await fetchPlaylist();
        }

        // Verificar sincronização global a cada intervalo
        if (isAutoplay && playlist && playlist.medias.length > 0) {
          const now = Date.now();
          const totalPlaylistDuration = playlist.medias.reduce((total, media) => total + (media.duracao * 1000), 0);
          const cycleStartTime = Math.floor(now / totalPlaylistDuration) * totalPlaylistDuration;

          let accumulatedTime = 0;
          let targetMediaIndex = 0;

          // Encontrar qual mídia deveria estar tocando agora
          for (let i = 0; i < playlist.medias.length; i++) {
            const mediaEndTime = accumulatedTime + (playlist.medias[i].duracao * 1000);
            if ((now - cycleStartTime) < mediaEndTime) {
              targetMediaIndex = i;
              break;
            }
            accumulatedTime += playlist.medias[i].duracao * 1000;
          }

          // Corrigir sincronização se necessário
          if (targetMediaIndex !== currentMediaIndex) {
            console.log(`Corrigindo sincronização: ${currentMediaIndex} -> ${targetMediaIndex}`);
            setPreviousMediaIndex(currentMediaIndex);
            setCurrentMediaIndex(targetMediaIndex);
          }
        }
      } catch (error) {
        console.error('Erro ao verificar atualizações:', error);
      }
    };

    const interval = setInterval(checkForUpdates, 5000); // Verificar a cada 5 segundos
    return () => clearInterval(interval);
  }, [id, cachedPlaylist, isLoading, currentMediaIndex, isAutoplay, playlist]);

  // Função para recarregar a playlist
  const fetchPlaylist = async () => {
    setIsLoading(true);

    try {
      const playlists = await loadPlaylistsFromStorage();
      let selectedPlaylist = playlists.find(p => p.nome === id);

      if (!selectedPlaylist) {
        selectedPlaylist = playlists.find(p => p.id === parseInt(id));
      }

      if (!selectedPlaylist) {
        setError(`Playlist "${id}" não encontrada. Verifique se a playlist existe.`);
        return;
      }

      if (!selectedPlaylist.midias || selectedPlaylist.midias.length === 0) {
        setError(`A playlist "${selectedPlaylist.nome}" está vazia. Adicione mídias à playlist.`);
        return;
      }

      // Verificar se a playlist mudou
      const playlistChanged = !cachedPlaylist ||
        cachedPlaylist.id !== selectedPlaylist.id ||
        JSON.stringify(selectedPlaylist.midias) !== JSON.stringify(cachedPlaylist.midias);

      // Se a playlist não mudou, manter o estado atual
      if (!playlistChanged) {
        return;
      }

      // Resetar estados apenas se a playlist mudou
      setCurrentMediaIndex(0);
      setPreviousMediaIndex(null);
      setProgress(0);

      // Formatar a playlist para o formato esperado pelo display
      const formattedPlaylist = {
        id: selectedPlaylist.id,
        nome: selectedPlaylist.nome,
        medias: selectedPlaylist.midias.map(midia => {
          let tipo = 'imagem';

          if (midia.type && midia.type.startsWith('video/')) {
            tipo = 'video';
          } else if (midia.arquivo && midia.arquivo.type && midia.arquivo.type.startsWith('video/')) {
            tipo = 'video';
          }

          let url = '';
          if (midia.originalUrl) {
            url = midia.originalUrl;
          } else if (midia.preview) {
            url = midia.preview;
          } else if (midia.arquivo && midia.arquivo.preview) {
            url = midia.arquivo.preview;
          } else if (midia.thumbnail) {
            url = midia.thumbnail;
          }

          let duracao = 10;
          if (tipo === 'video') {
            duracao = midia.duracao || midia.duration || 0;
          } else {
            duracao = midia.duracao || midia.duration || 10;
          }

          return {
            id: midia.id,
            nome: midia.nome,
            tipo: tipo,
            url: url,
            duracao: duracao
          };
        })
      };

      // Verificar e ajustar durações dos vídeos
      for (let i = 0; i < formattedPlaylist.medias.length; i++) {
        const media = formattedPlaylist.medias[i];
        if (media.tipo === 'video' && media.duracao === 0) {
          const video = document.createElement('video');
          video.crossOrigin = "anonymous";
          video.preload = "metadata";
          video.src = media.url;
          await new Promise((resolve) => {
            video.onloadedmetadata = () => {
              media.duracao = Math.ceil(video.duration);
              console.log(`Duração do vídeo ${media.nome}: ${media.duracao}s`);
              resolve();
            };
            video.onerror = (e) => {
              console.error(`Erro ao carregar metadados do vídeo ${media.nome}:`, e);
              media.duracao = 10;
              resolve();
            };
            setTimeout(() => {
              if (media.duracao === 0) {
                console.warn(`Timeout ao carregar metadados do vídeo ${media.nome}`);
                media.duracao = 10;
                resolve();
              }
            }, 5000);
          });
        }
      }

      setPlaylist(formattedPlaylist);
      setCachedPlaylist(selectedPlaylist);
    } catch (error) {
      console.error('Erro ao carregar playlist:', error);
      setError('Não foi possível carregar a playlist. Tente novamente mais tarde.');
    }
  };

  // Mostrar informações e controles ao clicar na tela
  const handleClick = () => {
    setShowInfo(true);
    setShowControls(true);

    // Esconder informações e controles após 5 segundos
    clearTimeout(window.infoTimeout);
    window.infoTimeout = setTimeout(() => {
      setShowInfo(false);
      setShowControls(false);
    }, 5000);
  };

  // Alternar entre reprodução automática e pausa
  const toggleAutoplay = (e) => {
    e.stopPropagation();
    setIsAutoplay(prev => !prev);
  };

  // Mostrar informações
  const toggleInfo = (e) => {
    e.stopPropagation();
    setShowInfo(prev => !prev);
  };

  return (
    <DisplayContainer
      onClick={handleClick}
      rotation={displayRotation}
      orientation={displayOrientation}
    >
      {isLoading ? (
        <LoadingContainer>
          <CircularProgress size={60} thickness={4} sx={{ color: '#007BFF', mb: 2 }} />
          <Typography variant="h6">Carregando conteúdo...</Typography>
        </LoadingContainer>
      ) : error ? (
        <LoadingContainer>
          <Box sx={{ textAlign: 'center', maxWidth: '80%' }}>
            <Typography variant="h5" color="error" gutterBottom>
              Ops! Algo deu errado
            </Typography>
            <Typography variant="body1" color="error" align="center" sx={{ mb: 3 }}>
              {error}
            </Typography>
            <Button
              variant="outlined"
              color="primary"
              onClick={() => window.location.reload()}
              sx={{ mt: 2 }}
            >
              Tentar novamente
            </Button>
          </Box>
        </LoadingContainer>
      ) : !playlist || playlist.medias.length === 0 ? (
        <LoadingContainer>
          <Box sx={{ textAlign: 'center', maxWidth: '80%' }}>
            <Typography variant="h5" gutterBottom>
              Nenhum conteúdo disponível
            </Typography>
            <Typography variant="body1" align="center" sx={{ mb: 3 }}>
              Não há mídias para exibir nesta playlist. Adicione conteúdo à playlist no painel de gerenciamento.
            </Typography>
            <Button
              variant="outlined"
              color="primary"
              onClick={() => window.location.reload()}
              sx={{ mt: 2 }}
            >
              Atualizar
            </Button>
          </Box>
        </LoadingContainer>
      ) : (
        <>
          {playlist.medias.map((media, index) => (
            <Fade
              key={media.id}
              in={index === currentMediaIndex}
              timeout={playlistConfig.transitionDuration}
              style={{
                display: index === currentMediaIndex || index === previousMediaIndex ? 'block' : 'none',
                zIndex: index === currentMediaIndex ? 2 : 1
              }}
            >
              <MediaContent
                orientation={orientation}
                mediaRotation={playlistRotation.getMediaRotation(media)}
                fitMode={media.fitMode || 'contain'}
                transitionDuration={playlistConfig.transitionDuration}
              >
                {media.tipo === 'imagem' ? (
                  <img
                    src={media.url}
                    alt={media.nome}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      maxWidth: '100vw',
                      maxHeight: '100vh',
                    }}
                  />
                ) : (
                  <video
                    ref={index === currentMediaIndex ? videoRef : null}
                    src={media.url}
                    autoPlay
                    muted
                    playsInline
                    controls={false}
                    loop={true}
                    preload="auto"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      maxWidth: '100vw',
                      maxHeight: '100vh',
                      backgroundColor: '#000',
                    }}
                    onLoadStart={() => {
                      console.log(`Iniciando carregamento do vídeo: ${media.nome}`);
                    }}
                    onLoadedData={() => {
                      console.log(`Vídeo carregado: ${media.nome}`);
                      // Forçar reprodução após carregamento e sincronizar tempo
                      if (index === currentMediaIndex && videoRef.current) {
                        // Calcular tempo correto do vídeo para sincronização
                        const now = Date.now();
                        const totalPlaylistDuration = playlist.medias.reduce((total, m) => total + (m.duracao * 1000), 0);
                        const cycleStartTime = Math.floor(now / totalPlaylistDuration) * totalPlaylistDuration;

                        let accumulatedTime = 0;
                        for (let i = 0; i < currentMediaIndex; i++) {
                          accumulatedTime += playlist.medias[i].duracao * 1000;
                        }

                        const mediaStartTime = cycleStartTime + accumulatedTime;
                        const timeInCurrentMedia = (now - mediaStartTime) / 1000;

                        // Definir tempo correto do vídeo para sincronização
                        if (timeInCurrentMedia > 0 && timeInCurrentMedia < media.duracao) {
                          videoRef.current.currentTime = timeInCurrentMedia % media.duracao;
                        }

                        videoRef.current.play().catch(e => console.error('Erro ao iniciar reprodução:', e));
                      }
                    }}
                    onError={(e) => {
                      console.error(`Erro ao carregar vídeo: ${media.nome}`, e.target.error);
                      // Em caso de erro, avançar para o próximo item
                      if (isAutoplay && index === currentMediaIndex) {
                        setPreviousMediaIndex(currentMediaIndex);
                        setCurrentMediaIndex((prevIndex) =>
                          prevIndex === playlist.medias.length - 1 ? 0 : prevIndex + 1
                        );
                      }
                    }}
                    onEnded={() => {
                      console.log(`Vídeo finalizado: ${media.nome}`);
                      if (isAutoplay) {
                        setPreviousMediaIndex(currentMediaIndex);
                        setCurrentMediaIndex((prevIndex) =>
                          prevIndex === playlist.medias.length - 1 ? 0 : prevIndex + 1
                        );
                        setProgress(0);
                      }
                    }}
                  />
                )}
              </MediaContent>
            </Fade>
          ))}

          <ControlsOverlay visible={showControls}>
            <IconButton
              onClick={toggleAutoplay}
              size="small"
              sx={{
                color: 'white',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.7)' }
              }}
            >
              {isAutoplay ? <PauseIcon /> : <PlayArrowIcon />}
            </IconButton>
            <IconButton
              onClick={toggleInfo}
              size="small"
              sx={{
                color: 'white',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.7)' }
              }}
            >
              <InfoIcon />
            </IconButton>
          </ControlsOverlay>

          <InfoOverlay visible={showInfo}>
            <Typography variant="h6">{playlist.medias[currentMediaIndex].nome}</Typography>
            <Typography variant="body2">
              {`${currentMediaIndex + 1}/${playlist.medias.length} • ${playlist.medias[currentMediaIndex].duracao}s`}
            </Typography>
          </InfoOverlay>

          <ProgressBar variant="determinate" value={progress} />
        </>
      )}
    </DisplayContainer>
  );
};

export default Display;