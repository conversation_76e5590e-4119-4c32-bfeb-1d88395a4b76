import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Paper,
  InputAdornment,
  Alert,
  Stack,
  Tooltip,
  Fade,
  LinearProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Snackbar,
} from '@mui/material';
import Navbar from '../../components/Navbar/Navbar';
import { useFormik } from 'formik';
import * as yup from 'yup';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import TvIcon from '@mui/icons-material/Tv';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import { useNavigate } from 'react-router-dom';
import { loadPlaylistsFromStorage, savePlaylistsToStorage } from '../../utils/storageUtils';
import RotationControl from '../../components/RotationControl/RotationControl';
import httpService from '../../services/httpService';
import { migrateTelasToDatabase, checkMigrationNeeded } from '../../utils/migrationUtils';
import {
  getCurrentUser,
  saveUserData,
  loadUserData,
  migrateGlobalDataToUser
} from '../../utils/userDataUtils';

const validationSchema = yup.object({
  nome: yup
    .string()
    .min(3, 'Nome deve ter no mínimo 3 caracteres')
    .required('Nome é obrigatório'),
  local: yup
    .string()
    .min(3, 'Local deve ter no mínimo 3 caracteres')
    .required('Local é obrigatório'),
  grupo: yup
    .string()
    .min(2, 'Grupo deve ter no mínimo 2 caracteres')
    .required('Grupo é obrigatório'),
  playlist: yup
    .string()
    .required('Playlist é obrigatória'),
  rotation: yup
    .number()
    .oneOf([0, 90, 180, 270, 360], 'Rotação deve ser 0, 90, 180, 270 ou 360 graus')
    .required('Rotação é obrigatória'),
  orientation: yup
    .string()
    .oneOf(['horizontal', 'vertical', 'auto'], 'Orientação deve ser horizontal, vertical ou auto')
    .required('Orientação é obrigatória'),
  // Removendo validação de URL já que é gerada automaticamente
});

const GerenciamentoTelas = () => {
  const navigate = useNavigate();
  const [telas, setTelas] = useState([]);
  const [playlists, setPlaylists] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedTela, setSelectedTela] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [status, setStatus] = useState({ type: '', message: '' });
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  // Carregar playlists e telas iniciais
  useEffect(() => {
    const loadData = async () => {
      try {
        // Verificar se usuário está logado
        const currentUser = getCurrentUser();
        if (!currentUser) {
          setSnackbar({
            open: true,
            message: 'Usuário não está logado. Redirecionando...',
            severity: 'error'
          });
          return;
        }

        console.log(`👤 Carregando dados para usuário: ${currentUser.name} (${currentUser.email})`);

        // Migrar dados globais para dados do usuário (se necessário)
        migrateGlobalDataToUser('playlists', 'playlists');
        migrateGlobalDataToUser('telas', 'telas');

        // Carregar playlists específicas do usuário
        const userPlaylists = loadUserData('playlists', []);
        setPlaylists(userPlaylists);

        // Carregar telas específicas do usuário
        const userTelas = loadUserData('telas', []);
        setTelas(userTelas);

        console.log(`📂 Playlists carregadas para ${currentUser.email}:`, userPlaylists);
        console.log(`📂 Telas carregadas para ${currentUser.email}:`, userTelas);
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        setSnackbar({
          open: true,
          message: 'Erro ao carregar dados. Verifique a conexão com o servidor.',
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const formik = useFormik({
    initialValues: {
      nome: '',
      local: '',
      grupo: '',
      playlist: '',
      rotation: 0,
      orientation: 'auto',
    },
    validationSchema: validationSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        console.log('Formulário enviado com valores:', values);

        // Verificar se há playlists disponíveis
        if (playlists.length === 0) {
          console.log('Nenhuma playlist disponível');
          setStatus({
            type: 'error',
            message: 'Não há playlists disponíveis. Por favor, crie uma playlist primeiro.'
          });
          return;
        }

        const novaTela = {
          id: selectedTela ? selectedTela.id : Date.now(),
          nome: values.nome,
          local: values.local,
          grupo: values.grupo,
          playlist: values.playlist,
          rotation: values.rotation,
          orientation: values.orientation,
          status: selectedTela ? selectedTela.status : 'inativo',
          url: `${window.location.origin}/display/${values.playlist}?autoplay=true&rotation=${values.rotation}&orientation=${values.orientation}`,
        };

        console.log('Tela a ser salva:', novaTela);

        let savedTela = novaTela;
        let updatedTelas;

        if (selectedTela) {
          // Atualizar tela existente
          updatedTelas = telas.map(tela => tela.id === selectedTela.id ? savedTela : tela);
        } else {
          // Criar nova tela
          updatedTelas = [...telas, savedTela];
        }

        // Salvar telas específicas do usuário
        setTelas(updatedTelas);
        saveUserData('telas', updatedTelas);

        console.log('Tela salva com sucesso para o usuário:', savedTela);

        resetForm();
        setSelectedTela(null);
        setDialogOpen(false);
        setStatus({
          type: 'success',
          message: selectedTela ? 'Tela atualizada com sucesso!' : 'Tela criada com sucesso!'
        });
        setTimeout(() => setStatus({ type: '', message: '' }), 3000);
      } catch (error) {
        console.error('Erro ao salvar tela:', error);
        setStatus({
          type: 'error',
          message: 'Erro ao salvar tela. Por favor, tente novamente.'
        });
      }
    },
  });

  const handleDelete = async (id) => {
    try {
      const updatedTelas = telas.filter(tela => tela.id !== id);
      setTelas(updatedTelas);
      saveUserData('telas', updatedTelas);

      setSnackbar({
        open: true,
        message: 'Tela removida com sucesso!',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erro ao remover tela:', error);
      setSnackbar({
        open: true,
        message: 'Erro ao remover tela. Tente novamente.',
        severity: 'error'
      });
    }
  };

  const handleEdit = (tela) => {
    console.log('Editando tela:', tela);
    setSelectedTela(tela);
    formik.setValues({
      nome: tela.nome,
      local: tela.local,
      grupo: tela.grupo,
      playlist: tela.playlist,
      rotation: tela.rotation || 0,
      orientation: tela.orientation || 'auto',
      // Não é necessário definir url, pois é gerada automaticamente
    });
    setDialogOpen(true);
  };

  const handleCopyUrl = (url) => {
    navigator.clipboard.writeText(url);
  };

  const handleOpenDisplay = (url) => {
    window.open(url, '_blank');
  };

  const toggleStatus = async (id) => {
    try {
      const tela = telas.find(t => t.id === id);
      if (!tela) return;

      const updatedTela = {
        ...tela,
        status: tela.status === 'ativo' ? 'inativo' : 'ativo'
      };

      const updatedTelas = telas.map(t => t.id === id ? updatedTela : t);
      setTelas(updatedTelas);
      saveUserData('telas', updatedTelas);

      setSnackbar({
        open: true,
        message: `Tela ${updatedTela.status === 'ativo' ? 'ativada' : 'desativada'} com sucesso!`,
        severity: 'success'
      });
    } catch (error) {
      console.error('Erro ao alterar status da tela:', error);
      setSnackbar({
        open: true,
        message: 'Erro ao alterar status da tela. Tente novamente.',
        severity: 'error'
      });
    }
  };

  const filteredTelas = telas.filter(tela =>
    tela.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tela.local.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tela.grupo.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <Navbar />
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity || 'info'}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Fade in={!loading}>
          <Box>
            {status.type && (
              <Alert severity={status.type} sx={{ mb: 2 }}>
                {status.message}
              </Alert>
            )}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h4" component="h1" gutterBottom sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                Gerenciamento de Telas
              </Typography>
              <Button
                variant="contained"
                onClick={() => setDialogOpen(true)}
                startIcon={<AddIcon />}
                sx={{
                  bgcolor: '#28a745',
                  '&:hover': { bgcolor: '#218838' },
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                }}
              >
                Nova Tela
              </Button>
            </Box>

            <Paper elevation={3} sx={{
              p: 2,
              mb: 3,
              borderRadius: '12px',
              background: 'white',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Pesquisar telas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    '&:hover fieldset': {
                      borderColor: '#007BFF',
                    },
                  },
                }}
              />
            </Paper>

            {loading ? (
              <Box sx={{ width: '100%', mt: 4 }}>
                <LinearProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {filteredTelas.map((tela) => (
                  <Grid item xs={12} md={6} key={tela.id}>
                    <Card elevation={3} sx={{
                      borderRadius: '12px',
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: '0 8px 16px rgba(0, 0, 0, 0.2)'
                      }
                    }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <TvIcon sx={{ fontSize: 40, mr: 2, color: '#007BFF' }} />
                            <Box>
                              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                                {tela.nome}
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                Local: {tela.local} | Grupo: {tela.grupo}
                              </Typography>
                            </Box>
                          </Box>
                          <Box>
                            <Tooltip title={tela.status === 'ativo' ? 'Desativar' : 'Ativar'}>
                              <IconButton
                                onClick={() => toggleStatus(tela.id)}
                                sx={{
                                  color: tela.status === 'ativo' ? '#28a745' : '#dc3545',
                                  mr: 1
                                }}
                              >
                                {tela.status === 'ativo' ? <PlayArrowIcon /> : <PauseIcon />}
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Editar">
                              <IconButton
                                onClick={() => handleEdit(tela)}
                                sx={{ color: '#007BFF', mr: 1 }}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Excluir">
                              <IconButton
                                onClick={() => handleDelete(tela.id)}
                                sx={{ color: '#dc3545' }}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                        <Typography variant="body2" color="textSecondary">
                          Playlist: {tela.playlist || 'Nenhuma playlist selecionada'}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            mt: 1,
                            color: tela.status === 'ativo' ? '#28a745' : '#dc3545',
                            fontWeight: 'bold'
                          }}
                        >
                          Status: {tela.status === 'ativo' ? 'Ativo' : 'Inativo'}
                        </Typography>
                        <Box sx={{ mt: 2, borderTop: '1px solid #eee', pt: 2 }}>
                          <Typography variant="body2" color="textSecondary" sx={{ mb: 1, wordBreak: 'break-all' }}>
                            URL: {tela.url}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title="Copiar URL">
                              <IconButton
                                onClick={() => handleCopyUrl(tela.url)}
                                size="small"
                                sx={{ color: '#007BFF' }}
                              >
                                <ContentCopyIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Abrir Display">
                              <IconButton
                                onClick={() => handleOpenDisplay(tela.url)}
                                size="small"
                                sx={{ color: '#28a745' }}
                              >
                                <OpenInNewIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={() => {
            setDialogOpen(false);
            setSelectedTela(null);
            formik.resetForm();
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{
            background: 'linear-gradient(45deg, #007BFF 30%, #00C49F 90%)',
            color: 'white',
            fontWeight: 'bold'
          }}>
            {selectedTela ? 'Editar Tela' : 'Nova Tela'}
          </DialogTitle>
          <form onSubmit={formik.handleSubmit}>
            <DialogContent sx={{ mt: 2 }}>
              <Stack spacing={2}>
                <TextField
                  fullWidth
                  label="Nome da Tela"
                  name="nome"
                  value={formik.values.nome}
                  onChange={formik.handleChange}
                  error={formik.touched.nome && Boolean(formik.errors.nome)}
                  helperText={formik.touched.nome && formik.errors.nome}
                />
                <TextField
                  fullWidth
                  label="Local"
                  name="local"
                  value={formik.values.local}
                  onChange={formik.handleChange}
                  error={formik.touched.local && Boolean(formik.errors.local)}
                  helperText={formik.touched.local && formik.errors.local}
                />
                <TextField
                  fullWidth
                  label="Grupo"
                  name="grupo"
                  value={formik.values.grupo}
                  onChange={formik.handleChange}
                  error={formik.touched.grupo && Boolean(formik.errors.grupo)}
                  helperText={formik.touched.grupo && formik.errors.grupo}
                />
                <FormControl fullWidth error={formik.touched.playlist && Boolean(formik.errors.playlist)}>
                  <InputLabel>Playlist</InputLabel>
                  <Select
                    name="playlist"
                    value={formik.values.playlist}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  >
                    {playlists.length > 0 ? (
                      playlists.map((playlist) => (
                        <MenuItem key={playlist.id} value={playlist.nome}>
                          {playlist.nome}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled value="">
                        Nenhuma playlist disponível
                      </MenuItem>
                    )}
                  </Select>
                  {formik.touched.playlist && formik.errors.playlist && (
                    <Typography variant="caption" color="error">
                      {formik.errors.playlist}
                    </Typography>
                  )}
                </FormControl>

                {/* Configurações de Rotação da Tela */}
                <RotationControl
                  rotation={formik.values.rotation}
                  orientation={formik.values.orientation}
                  onRotationChange={(rotation) => formik.setFieldValue('rotation', rotation)}
                  onOrientationChange={(orientation) => formik.setFieldValue('orientation', orientation)}
                  disabled={false}
                  showPreview={true}
                  compact={false}
                />
              </Stack>
            </DialogContent>
            <DialogActions sx={{ p: 2 }}>
              <Button
                onClick={() => {
                  setDialogOpen(false);
                  setSelectedTela(null);
                  formik.resetForm();
                }}
                sx={{ color: '#dc3545' }}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                variant="contained"
                sx={{
                  bgcolor: '#28a745',
                  '&:hover': { bgcolor: '#218838' },
                }}
              >
                {selectedTela ? 'Salvar' : 'Criar'}
              </Button>
            </DialogActions>
          </form>
        </Dialog>
      </Container>
    </>
  );
};

export default GerenciamentoTelas;