import express from 'express';
import cors from 'cors';
import { MongoClient, ObjectId, GridFSBucket } from 'mongodb';
// Removido import do MongoMemoryServer pois usaremos MongoDB local
import multer from 'multer';
import { Readable } from 'stream';

const app = express();
const port = 3001;

app.use(cors());
app.use(express.json({ limit: '50mb' }));

let db = null;
let bucket = null;

// Conectar ao MongoDB local
async function connectToMongo() {
  if (!db) {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
    const client = await MongoClient.connect(mongoUri);
    db = client.db('displaydb');
    bucket = new GridFSBucket(db);
    console.log('Conectado ao MongoDB local com sucesso');
  }
  return db;
}

// Configuração do Multer para upload de arquivos
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

// Rota para salvar mídia
app.post('/api/medias', upload.single('file'), async (req, res) => {
  try {
    await connectToMongo();

    const file = req.file;
    if (!file) {
      return res.status(400).json({ error: 'Nenhum arquivo enviado' });
    }

    let mediaData;

    // Verificar se os dados da mídia estão no formato JSON ou como campos individuais
    if (req.body.mediaData) {
      try {
        mediaData = JSON.parse(req.body.mediaData);
      } catch (e) {
        console.error('Erro ao parsear mediaData:', e);
        mediaData = req.body;
      }
    } else {
      mediaData = req.body;
    }

    const { nome, type, lastModified, size, originalUrl } = mediaData;

    const uploadStream = bucket.openUploadStream(nome, {
      metadata: { type, lastModified, size, originalUrl }
    });

    const readableStream = new Readable();
    readableStream.push(file.buffer);
    readableStream.push(null);

    readableStream.pipe(uploadStream);

    uploadStream.on('finish', async () => {
      const mediaDoc = {
        _id: uploadStream.id,
        nome,
        type,
        lastModified,
        size,
        fileId: uploadStream.id,
        originalUrl,
        // Configurações de rotação padrão
        rotation: 0,
        orientation: 'auto',
        fitMode: 'cover',
        duration: type && type.startsWith('image/') ? 10 : 0
      };

      await db.collection('midias').insertOne(mediaDoc);

      // Para vídeos, retornar com preview URL ABSOLUTA, para imagens, gerar base64
      let preview = `http://localhost:3001/api/media/${uploadStream.id}`;
      if (type && type.startsWith('image/')) {
        try {
          // Gerar preview base64 para imagens
          const downloadStream = bucket.openDownloadStream(uploadStream.id);
          const chunks = [];

          await new Promise((resolve, reject) => {
            downloadStream.on('data', (chunk) => chunks.push(chunk));
            downloadStream.on('end', resolve);
            downloadStream.on('error', reject);
          });

          const buffer = Buffer.concat(chunks);
          preview = `data:${type};base64,${buffer.toString('base64')}`;
        } catch (error) {
          console.error('Erro ao gerar preview para imagem:', error);
          // Manter URL ABSOLUTA em caso de erro
          preview = `http://localhost:3001/api/media/${uploadStream.id}`;
        }
      }

      const responseData = {
        ...mediaDoc,
        id: mediaDoc._id,
        preview,
        originalUrl: preview
      };

      console.log('Mídia salva com sucesso:', responseData);
      res.json(responseData);
    });
  } catch (error) {
    console.error('Erro ao salvar mídia:', error);
    res.status(500).json({ error: 'Erro ao salvar mídia', details: error.message });
  }
});

// Rota para obter mídia
app.get('/api/media/:id', async (req, res) => {
  try {
    console.log(`Requisição para mídia ID: ${req.params.id}`);
    await connectToMongo();
    const id = new ObjectId(req.params.id);

    const media = await db.collection('midias').findOne({ _id: id });
    console.log(`Mídia encontrada:`, media);

    if (!media) {
      console.log(`Mídia não encontrada para ID: ${req.params.id}`);
      return res.status(404).json({ error: 'Mídia não encontrada' });
    }

    // Usar o fileId para fazer download do GridFS, não o _id da mídia
    const fileId = media.fileId || media._id;
    console.log(`Tentando fazer download do arquivo com fileId: ${fileId}`);

    // Verificar se o arquivo existe no GridFS
    try {
      const fileInfo = await bucket.find({ _id: fileId }).toArray();
      console.log(`Arquivo no GridFS:`, fileInfo);

      if (fileInfo.length === 0) {
        console.log(`Arquivo não encontrado no GridFS para fileId: ${fileId}`);
        return res.status(404).json({ error: 'Arquivo não encontrado no GridFS' });
      }
    } catch (gridError) {
      console.error('Erro ao verificar arquivo no GridFS:', gridError);
    }

    const downloadStream = bucket.openDownloadStream(fileId);

    res.set('Content-Type', media.type);
    res.set('Accept-Ranges', 'bytes'); // Importante para vídeos

    // Adicionar tratamento de erro para o stream
    downloadStream.on('error', (error) => {
      console.error('Erro ao fazer download do arquivo:', error);
      if (!res.headersSent) {
        res.status(404).json({ error: 'Arquivo não encontrado no GridFS' });
      }
    });

    downloadStream.on('file', (file) => {
      console.log(`Iniciando download do arquivo: ${file.filename}, tamanho: ${file.length}`);
    });

    downloadStream.pipe(res);
  } catch (error) {
    console.error('Erro ao obter mídia:', error);
    if (!res.headersSent) {
      res.status(500).json({ error: 'Erro ao obter mídia' });
    }
  }
});

// Rota para listar todas as mídias
app.get('/api/medias', async (req, res) => {
  try {
    await connectToMongo();
    const midias = await db.collection('midias').find().toArray();

    // Para cada mídia, gerar preview apropriado
    const midiasWithPreviews = await Promise.all(midias.map(async (midia) => {
      let preview = `http://localhost:3001/api/media/${midia.fileId || midia._id}`;

      // Se for uma imagem, gerar preview base64
      if (midia.type && midia.type.startsWith('image/')) {
        try {
          const downloadStream = bucket.openDownloadStream(midia.fileId);
          const chunks = [];

          await new Promise((resolve, reject) => {
            downloadStream.on('data', (chunk) => chunks.push(chunk));
            downloadStream.on('end', resolve);
            downloadStream.on('error', reject);
          });

          const buffer = Buffer.concat(chunks);
          preview = `data:${midia.type};base64,${buffer.toString('base64')}`;
        } catch (error) {
          console.error('Erro ao gerar preview para mídia:', midia._id, error);
          // Manter a URL original em caso de erro
        }
      }
      // Para vídeos, manter URL ABSOLUTA do servidor
      else if (midia.type && midia.type.startsWith('video/')) {
        preview = `http://localhost:3001/api/media/${midia.fileId || midia._id}`;
      }

      return {
        ...midia,
        id: midia._id,
        preview,
        originalUrl: preview,
        arquivo: {
          type: midia.type,
          size: midia.size,
          lastModified: midia.lastModified,
          preview
        }
      };
    }));

    res.json(midiasWithPreviews);
  } catch (error) {
    console.error('Erro ao listar mídias:', error);
    res.status(500).json({ error: 'Erro ao listar mídias' });
  }
});

// Rota para remover mídia
app.delete('/api/medias/:id', async (req, res) => {
  try {
    console.log(`Tentando remover mídia com ID: ${req.params.id}`);
    await connectToMongo();
    const idParam = req.params.id;

    // Primeiro, encontrar a mídia para obter o fileId
    let media;
    let query;

    if (ObjectId.isValid(idParam) && idParam.length === 24 && /^[0-9a-fA-F]{24}$/.test(idParam)) {
      query = { _id: new ObjectId(idParam) };
      media = await db.collection('midias').findOne(query);
    } else {
      // Se não for um ObjectId válido, buscar por outros campos
      query = { $or: [{ id: idParam }, { id: parseInt(idParam) }] };
      media = await db.collection('midias').findOne(query);
    }

    if (!media) {
      console.log(`Mídia não encontrada para ID: ${idParam}`);
      return res.status(404).json({ error: 'Mídia não encontrada' });
    }

    console.log(`Mídia encontrada:`, media);

    // Remover arquivo do GridFS usando fileId
    const fileId = media.fileId || media._id;
    try {
      await bucket.delete(fileId);
      console.log(`Arquivo removido do GridFS: ${fileId}`);
    } catch (gridError) {
      console.error('Erro ao remover arquivo do GridFS:', gridError);
      // Continuar mesmo se não conseguir remover do GridFS
    }

    // Remover documento da coleção midias
    const result = await db.collection('midias').deleteOne({ _id: media._id });

    if (result.deletedCount === 0) {
      console.log(`Falha ao remover documento da coleção midias`);
      return res.status(500).json({ error: 'Falha ao remover mídia da base de dados' });
    }

    console.log(`Mídia removida com sucesso: ${idParam}`);
    res.json({ message: 'Mídia removida com sucesso' });
  } catch (error) {
    console.error('Erro ao remover mídia:', error);
    res.status(500).json({ error: 'Erro ao remover mídia', details: error.message });
  }
});

// Rotas para playlists
app.post('/api/playlists', async (req, res) => {
  try {
    await connectToMongo();
    const playlist = req.body;

    // Remover _id se existir para evitar conflitos
    const { _id, ...playlistToSave } = playlist;

    // Se a playlist já tem um ID (timestamp), preservá-lo
    if (playlist.id) {
      const result = await db.collection('playlists').insertOne(playlistToSave);
      res.json({ ...playlistToSave, _id: result.insertedId });
    } else {
      const result = await db.collection('playlists').insertOne(playlistToSave);
      res.json({ ...playlistToSave, id: result.insertedId });
    }
  } catch (error) {
    console.error('Erro ao salvar playlist:', error);
    res.status(500).json({ error: 'Erro ao salvar playlist', details: error.message });
  }
});

app.get('/api/playlists', async (req, res) => {
  try {
    await connectToMongo();
    const playlists = await db.collection('playlists').find().toArray();
    res.json(playlists.map(playlist => ({ ...playlist, id: playlist._id })));
  } catch (error) {
    console.error('Erro ao listar playlists:', error);
    res.status(500).json({ error: 'Erro ao listar playlists' });
  }
});

app.put('/api/playlists/:id', async (req, res) => {
  try {
    await connectToMongo();
    const idParam = req.params.id;
    const playlist = req.body;

    // Verificar se o ID é um ObjectId válido (24 caracteres hexadecimais)
    let query;
    if (ObjectId.isValid(idParam) && idParam.length === 24 && /^[0-9a-fA-F]{24}$/.test(idParam)) {
      query = { _id: new ObjectId(idParam) };
    } else {
      // Se não for um ObjectId válido, buscar por outros campos
      query = { $or: [{ id: idParam }, { id: parseInt(idParam) }, { nome: idParam }] };
    }

    // Remover _id do objeto playlist para evitar erro de campo imutável
    const { _id, ...playlistUpdate } = playlist;

    const result = await db.collection('playlists').updateOne(
      query,
      { $set: playlistUpdate }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({ error: 'Playlist não encontrada' });
    }

    res.json({ ...playlist, id: idParam });
  } catch (error) {
    console.error('Erro ao atualizar playlist:', error);
    res.status(500).json({ error: 'Erro ao atualizar playlist' });
  }
});

app.delete('/api/playlists/:id', async (req, res) => {
  try {
    await connectToMongo();
    const idParam = req.params.id;

    // Verificar se o ID é um ObjectId válido (24 caracteres hexadecimais)
    let query;
    if (ObjectId.isValid(idParam) && idParam.length === 24 && /^[0-9a-fA-F]{24}$/.test(idParam)) {
      query = { _id: new ObjectId(idParam) };
    } else {
      // Se não for um ObjectId válido, buscar por outros campos
      query = { $or: [{ id: idParam }, { id: parseInt(idParam) }, { nome: idParam }] };
    }

    const result = await db.collection('playlists').deleteOne(query);

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: 'Playlist não encontrada' });
    }

    res.json({ message: 'Playlist removida com sucesso' });
  } catch (error) {
    console.error('Erro ao remover playlist:', error);
    res.status(500).json({ error: 'Erro ao remover playlist' });
  }
});

// Rotas para telas
app.post('/api/telas', async (req, res) => {
  try {
    await connectToMongo();
    const tela = req.body;

    // Remover _id se existir para evitar conflitos
    const { _id, ...telaToSave } = tela;

    // Se a tela já tem um ID (timestamp), preservá-lo
    if (tela.id) {
      const result = await db.collection('telas').insertOne(telaToSave);
      res.json({ ...telaToSave, _id: result.insertedId });
    } else {
      const result = await db.collection('telas').insertOne(telaToSave);
      res.json({ ...telaToSave, id: result.insertedId });
    }
  } catch (error) {
    console.error('Erro ao salvar tela:', error);
    res.status(500).json({ error: 'Erro ao salvar tela', details: error.message });
  }
});

app.get('/api/telas', async (req, res) => {
  try {
    await connectToMongo();
    const telas = await db.collection('telas').find().toArray();
    res.json(telas.map(tela => ({ ...tela, id: tela._id })));
  } catch (error) {
    console.error('Erro ao listar telas:', error);
    res.status(500).json({ error: 'Erro ao listar telas' });
  }
});

app.put('/api/telas/:id', async (req, res) => {
  try {
    await connectToMongo();
    const idParam = req.params.id;
    const tela = req.body;

    // Verificar se o ID é um ObjectId válido (24 caracteres hexadecimais)
    let query;
    if (ObjectId.isValid(idParam) && idParam.length === 24 && /^[0-9a-fA-F]{24}$/.test(idParam)) {
      query = { _id: new ObjectId(idParam) };
    } else {
      // Se não for um ObjectId válido, buscar por outros campos
      query = { $or: [{ id: idParam }, { id: parseInt(idParam) }] };
    }

    // Remover _id do objeto tela para evitar erro de campo imutável
    const { _id, ...telaUpdate } = tela;

    const result = await db.collection('telas').updateOne(
      query,
      { $set: telaUpdate }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({ error: 'Tela não encontrada' });
    }

    res.json({ ...tela, id: idParam });
  } catch (error) {
    console.error('Erro ao atualizar tela:', error);
    res.status(500).json({ error: 'Erro ao atualizar tela' });
  }
});

app.delete('/api/telas/:id', async (req, res) => {
  try {
    await connectToMongo();
    const idParam = req.params.id;

    // Verificar se o ID é um ObjectId válido (24 caracteres hexadecimais)
    let query;
    if (ObjectId.isValid(idParam) && idParam.length === 24 && /^[0-9a-fA-F]{24}$/.test(idParam)) {
      query = { _id: new ObjectId(idParam) };
    } else {
      // Se não for um ObjectId válido, buscar por outros campos
      query = { $or: [{ id: idParam }, { id: parseInt(idParam) }] };
    }

    const result = await db.collection('telas').deleteOne(query);

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: 'Tela não encontrada' });
    }

    res.json({ message: 'Tela removida com sucesso' });
  } catch (error) {
    console.error('Erro ao remover tela:', error);
    res.status(500).json({ error: 'Erro ao remover tela' });
  }
});

app.listen(port, () => {
  console.log(`Servidor rodando na porta ${port}`);
});