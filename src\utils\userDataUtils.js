// Utilitários para isolamento de dados por usuário (multi-tenant)

/**
 * Obtém o ID do usuário atual logado
 * @returns {string|null} ID do usuário ou null se não estiver logado
 */
export const getCurrentUserId = () => {
  const userEmail = localStorage.getItem('userEmail');
  if (!userEmail) return null;

  // Usar email como identificador único do usuário
  return userEmail;
};

/**
 * Obtém informações do usuário atual
 * @returns {object|null} Dados do usuário ou null se não estiver logado
 */
export const getCurrentUser = () => {
  const userEmail = localStorage.getItem('userEmail');
  const userName = localStorage.getItem('userName');
  const isAdmin = localStorage.getItem('isAdmin') === 'true';

  if (!userEmail) return null;

  return {
    id: userEmail,
    email: userEmail,
    name: userName,
    isAdmin: isAdmin
  };
};

/**
 * Gera chave de armazenamento específica para o usuário
 * @param {string} dataType - Tipo de dados (ex: 'telas', 'playlists', 'midias')
 * @param {string} userId - ID do usuário (opcional, usa o atual se não fornecido)
 * @returns {string} Chave de armazenamento
 */
export const getUserStorageKey = (dataType, userId = null) => {
  const currentUserId = userId || getCurrentUserId();
  if (!currentUserId) {
    throw new Error('Usuário não está logado');
  }

  // Criar chave única: dataType_userEmail
  return `${dataType}_${currentUserId}`;
};

/**
 * Comprime uma string base64 removendo metadados desnecessários
 * @param {string} base64String - String base64 para comprimir
 * @returns {string} - String base64 comprimida
 */
const compressBase64 = (base64String) => {
  if (!base64String || !base64String.startsWith('data:')) {
    return base64String;
  }

  // Remover o prefixo data:type/subtype;base64, para economizar espaço
  const base64Data = base64String.split(',')[1];
  return base64Data;
};

/**
 * Otimiza dados de mídia para reduzir o tamanho no localStorage
 * @param {Array} data - Array de mídias
 * @returns {Array} - Array de mídias otimizado
 */
const optimizeMediaData = (data) => {
  if (!Array.isArray(data)) return data;

  return data.map(item => {
    const optimizedItem = { ...item };

    // Comprimir preview se for base64
    if (optimizedItem.preview && optimizedItem.preview.startsWith('data:')) {
      optimizedItem.preview = compressBase64(optimizedItem.preview);
      optimizedItem._compressed = true;
      optimizedItem._originalType = optimizedItem.type; // Salvar tipo para restauração
    }

    // Comprimir originalUrl se for base64
    if (optimizedItem.originalUrl && optimizedItem.originalUrl.startsWith('data:')) {
      optimizedItem.originalUrl = compressBase64(optimizedItem.originalUrl);
      optimizedItem._compressedOriginal = true;
    }

    // Comprimir arquivo.preview se existir
    if (optimizedItem.arquivo && optimizedItem.arquivo.preview && optimizedItem.arquivo.preview.startsWith('data:')) {
      optimizedItem.arquivo.preview = compressBase64(optimizedItem.arquivo.preview);
      optimizedItem.arquivo._compressed = true;
    }

    return optimizedItem;
  });
};

/**
 * Calcula o tamanho dos dados em bytes
 * @param {any} data - Dados para calcular o tamanho
 * @returns {number} - Tamanho em bytes
 */
const calculateDataSize = (data) => {
  return new Blob([JSON.stringify(data)]).size;
};

/**
 * Salva dados específicos do usuário no localStorage
 * @param {string} dataType - Tipo de dados
 * @param {any} data - Dados para salvar
 * @param {string} userId - ID do usuário (opcional)
 * @returns {boolean} Sucesso da operação
 */
export const saveUserData = (dataType, data, userId = null) => {
  try {
    const key = getUserStorageKey(dataType, userId);

    // Otimizar dados se for mídia ou playlists
    let dataToSave = data;
    if (dataType === 'midias' || dataType === 'playlists') {
      dataToSave = optimizeMediaData(data);

      const originalSize = calculateDataSize(data);
      const compressedSize = calculateDataSize(dataToSave);
      const compressionRatio = originalSize > 0 ? ((originalSize - compressedSize) / originalSize * 100).toFixed(1) : 0;

      console.log(`📊 Compressão de dados:`, {
        original: `${(originalSize / 1024).toFixed(1)}KB`,
        comprimido: `${(compressedSize / 1024).toFixed(1)}KB`,
        economia: `${compressionRatio}%`
      });
    }

    const jsonString = JSON.stringify(dataToSave);

    // Verificar se ainda assim excede a quota (5MB limite)
    if (jsonString.length > 5 * 1024 * 1024) {
      console.warn('⚠️ Dados muito grandes mesmo após compressão. Aplicando limpeza...');

      // Se for mídias, manter apenas as 20 mais recentes
      if (dataType === 'midias' && Array.isArray(dataToSave)) {
        dataToSave = dataToSave.slice(-20);
        console.log('🧹 Mantendo apenas as 20 mídias mais recentes');
      }

      // Se for playlists, limitar a 10 playlists
      if (dataType === 'playlists' && Array.isArray(dataToSave)) {
        dataToSave = dataToSave.slice(-10);
        console.log('🧹 Mantendo apenas as 10 playlists mais recentes');
      }
    }

    localStorage.setItem(key, JSON.stringify(dataToSave));
    console.log(`💾 Dados salvos para usuário: ${key}`, {
      items: Array.isArray(dataToSave) ? dataToSave.length : 'N/A',
      size: `${(JSON.stringify(dataToSave).length / 1024).toFixed(1)}KB`
    });
    return true;
  } catch (error) {
    console.error('Erro ao salvar dados do usuário:', error);

    // Se for erro de quota, tentar limpeza automática
    if (error.name === 'QuotaExceededError') {
      console.log('🧹 Tentando limpeza automática do localStorage...');
      try {
        // Limpar dados antigos de outros usuários
        const currentUserId = getCurrentUserId();
        const keysToRemove = [];

        for (let i = 0; i < localStorage.length; i++) {
          const storageKey = localStorage.key(i);
          if (storageKey && (storageKey.includes('midias_') || storageKey.includes('playlists_')) && !storageKey.includes(currentUserId)) {
            keysToRemove.push(storageKey);
          }
        }

        keysToRemove.forEach(keyToRemove => localStorage.removeItem(keyToRemove));
        console.log(`🧹 Removidas ${keysToRemove.length} entradas antigas`);

        // Tentar salvar novamente com dados reduzidos
        let reducedData = dataToSave;
        if (dataType === 'midias' && Array.isArray(reducedData)) {
          reducedData = reducedData.slice(-10); // Apenas 10 mídias
        }
        if (dataType === 'playlists' && Array.isArray(reducedData)) {
          reducedData = reducedData.slice(-5); // Apenas 5 playlists
        }

        localStorage.setItem(key, JSON.stringify(reducedData));
        console.log('✅ Dados salvos após limpeza com dados reduzidos');
        return true;
      } catch (retryError) {
        console.error('❌ Falha mesmo após limpeza:', retryError);
        return false;
      }
    }

    return false;
  }
};

/**
 * Descomprime uma string base64 adicionando metadados padrão
 * @param {string} compressedBase64 - String base64 comprimida
 * @param {string} type - Tipo MIME do arquivo
 * @returns {string} - String base64 completa
 */
const decompressBase64 = (compressedBase64, type = 'video/mp4') => {
  if (!compressedBase64 || compressedBase64.startsWith('data:')) {
    return compressedBase64;
  }

  return `data:${type};base64,${compressedBase64}`;
};

/**
 * Restaura dados de mídia comprimidos
 * @param {Array} data - Array de mídias comprimido
 * @returns {Array} - Array de mídias restaurado
 */
const restoreMediaData = (data) => {
  if (!Array.isArray(data)) return data;

  return data.map(item => {
    const restoredItem = { ...item };

    // Restaurar preview se foi comprimido
    if (restoredItem._compressed && restoredItem.preview) {
      const type = restoredItem._originalType || restoredItem.type || 'video/mp4';
      restoredItem.preview = decompressBase64(restoredItem.preview, type);
      delete restoredItem._compressed;
      delete restoredItem._originalType;
    }

    // Restaurar originalUrl se foi comprimido
    if (restoredItem._compressedOriginal && restoredItem.originalUrl) {
      const type = restoredItem.type || 'video/mp4';
      restoredItem.originalUrl = decompressBase64(restoredItem.originalUrl, type);
      delete restoredItem._compressedOriginal;
    }

    // Restaurar arquivo.preview se foi comprimido
    if (restoredItem.arquivo && restoredItem.arquivo._compressed && restoredItem.arquivo.preview) {
      const type = restoredItem.type || 'video/mp4';
      restoredItem.arquivo.preview = decompressBase64(restoredItem.arquivo.preview, type);
      delete restoredItem.arquivo._compressed;
    }

    return restoredItem;
  });
};

/**
 * Carrega dados específicos do usuário do localStorage
 * @param {string} dataType - Tipo de dados
 * @param {any} defaultValue - Valor padrão se não houver dados
 * @param {string} userId - ID do usuário (opcional)
 * @returns {any} Dados do usuário ou valor padrão
 */
export const loadUserData = (dataType, defaultValue = [], userId = null) => {
  try {
    const key = getUserStorageKey(dataType, userId);
    const data = localStorage.getItem(key);

    if (data) {
      let parsedData = JSON.parse(data);

      // Restaurar dados comprimidos se for mídia ou playlists
      if (dataType === 'midias' || dataType === 'playlists') {
        parsedData = restoreMediaData(parsedData);
      }

      console.log(`📂 Dados carregados para usuário: ${key}`, parsedData);
      return parsedData;
    }

    console.log(`📂 Nenhum dado encontrado para: ${key}, usando valor padrão`);
    return defaultValue;
  } catch (error) {
    console.error('Erro ao carregar dados do usuário:', error);
    return defaultValue;
  }
};

/**
 * Remove dados específicos do usuário
 * @param {string} dataType - Tipo de dados
 * @param {string} userId - ID do usuário (opcional)
 * @returns {boolean} Sucesso da operação
 */
export const removeUserData = (dataType, userId = null) => {
  try {
    const key = getUserStorageKey(dataType, userId);
    localStorage.removeItem(key);
    console.log(`🗑️ Dados removidos para usuário: ${key}`);
    return true;
  } catch (error) {
    console.error('Erro ao remover dados do usuário:', error);
    return false;
  }
};

/**
 * Lista todas as chaves de dados de um usuário
 * @param {string} userId - ID do usuário (opcional)
 * @returns {string[]} Array de chaves de dados
 */
export const getUserDataKeys = (userId = null) => {
  const currentUserId = userId || getCurrentUserId();
  if (!currentUserId) return [];

  const keys = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.endsWith(`_${currentUserId}`)) {
      keys.push(key);
    }
  }

  return keys;
};

/**
 * Migra dados globais para dados específicos do usuário
 * @param {string} dataType - Tipo de dados
 * @param {string} globalKey - Chave global antiga
 * @returns {boolean} Sucesso da migração
 */
export const migrateGlobalDataToUser = (dataType, globalKey) => {
  try {
    const currentUserId = getCurrentUserId();
    if (!currentUserId) {
      console.warn('Usuário não logado, não é possível migrar dados');
      return false;
    }

    // Verificar se já existe dados do usuário
    const userKey = getUserStorageKey(dataType);
    const existingUserData = localStorage.getItem(userKey);

    if (existingUserData) {
      console.log(`Dados do usuário já existem para: ${userKey}`);
      return true;
    }

    // Carregar dados globais
    const globalData = localStorage.getItem(globalKey);
    if (globalData) {
      // Migrar para dados do usuário
      localStorage.setItem(userKey, globalData);
      console.log(`✅ Dados migrados de ${globalKey} para ${userKey}`);

      // Opcional: remover dados globais após migração
      // localStorage.removeItem(globalKey);

      return true;
    }

    console.log(`Nenhum dado global encontrado em: ${globalKey}`);
    return false;
  } catch (error) {
    console.error('Erro ao migrar dados globais:', error);
    return false;
  }
};

/**
 * Limpa todos os dados do usuário atual
 * @returns {boolean} Sucesso da operação
 */
export const clearUserData = () => {
  try {
    const keys = getUserDataKeys();
    keys.forEach(key => localStorage.removeItem(key));
    console.log(`🧹 Todos os dados do usuário removidos: ${keys.length} itens`);
    return true;
  } catch (error) {
    console.error('Erro ao limpar dados do usuário:', error);
    return false;
  }
};

/**
 * Limpa dados globais antigos que podem interferir no sistema multi-tenant
 * @returns {boolean} Sucesso da operação
 */
export const clearGlobalData = () => {
  try {
    const globalKeys = ['telas', 'playlists', 'midias', 'users'];
    let removedCount = 0;

    globalKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        removedCount++;
        console.log(`🧹 Dados globais removidos: ${key}`);
      }
    });

    console.log(`🧹 Total de dados globais removidos: ${removedCount} itens`);
    return true;
  } catch (error) {
    console.error('Erro ao limpar dados globais:', error);
    return false;
  }
};

/**
 * Força a migração de dados globais para o usuário atual
 * @returns {boolean} Sucesso da operação
 */
export const forceMigrateAllData = () => {
  try {
    const currentUser = getCurrentUser();
    if (!currentUser) {
      console.warn('Usuário não logado, não é possível migrar dados');
      return false;
    }

    console.log(`🔄 Forçando migração de todos os dados para: ${currentUser.email}`);

    // Migrar todos os tipos de dados
    const dataTypes = [
      { type: 'telas', globalKey: 'telas' },
      { type: 'playlists', globalKey: 'playlists' },
      { type: 'midias', globalKey: 'midias' }
    ];

    dataTypes.forEach(({ type, globalKey }) => {
      migrateGlobalDataToUser(type, globalKey);
    });

    return true;
  } catch (error) {
    console.error('Erro ao forçar migração:', error);
    return false;
  }
};

/**
 * Limpa automaticamente dados antigos para liberar espaço
 * @returns {boolean} Sucesso da operação
 */
export const cleanupOldData = () => {
  try {
    const currentUserId = getCurrentUserId();
    if (!currentUserId) return false;

    console.log('🧹 Iniciando limpeza automática do localStorage...');

    // Limpar dados de outros usuários
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('midias_') || key.includes('playlists_')) && !key.includes(currentUserId)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log(`🧹 Removidas ${keysToRemove.length} entradas de outros usuários`);

    // Limitar dados do usuário atual
    const userMidias = loadUserData('midias', []);
    const userPlaylists = loadUserData('playlists', []);

    if (userMidias.length > 15) {
      const reducedMidias = userMidias.slice(-15);
      saveUserData('midias', reducedMidias);
      console.log(`🧹 Reduzidas mídias de ${userMidias.length} para ${reducedMidias.length}`);
    }

    if (userPlaylists.length > 8) {
      const reducedPlaylists = userPlaylists.slice(-8);
      saveUserData('playlists', reducedPlaylists);
      console.log(`🧹 Reduzidas playlists de ${userPlaylists.length} para ${reducedPlaylists.length}`);
    }

    return true;
  } catch (error) {
    console.error('Erro na limpeza automática:', error);
    return false;
  }
};

/**
 * Função de debug para verificar todos os dados no localStorage
 * @returns {object} Relatório completo dos dados
 */
export const debugLocalStorage = () => {
  const currentUser = getCurrentUser();
  const report = {
    currentUser: currentUser,
    globalData: {},
    userData: {},
    allKeys: []
  };

  // Verificar dados globais
  const globalKeys = ['telas', 'playlists', 'midias', 'users'];
  globalKeys.forEach(key => {
    const data = localStorage.getItem(key);
    if (data) {
      try {
        report.globalData[key] = JSON.parse(data);
      } catch (e) {
        report.globalData[key] = data;
      }
    }
  });

  // Verificar dados do usuário atual
  if (currentUser) {
    const userDataTypes = ['telas', 'playlists', 'midias'];
    userDataTypes.forEach(type => {
      try {
        const userKey = getUserStorageKey(type);
        const data = localStorage.getItem(userKey);
        if (data) {
          report.userData[type] = JSON.parse(data);
        }
      } catch (e) {
        console.error(`Erro ao carregar dados do usuário para ${type}:`, e);
      }
    });
  }

  // Listar todas as chaves
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key) {
      report.allKeys.push(key);
    }
  }

  console.log('🔍 DEBUG - Relatório completo do localStorage:', report);
  return report;
};