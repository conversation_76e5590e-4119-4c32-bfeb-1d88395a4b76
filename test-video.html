<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Vídeo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Teste de Carregamento de Vídeos</h1>
        
        <div class="test-section">
            <h2>Teste 1: Vídeo Direto do Servidor</h2>
            <p>Testando carregamento direto da URL do servidor:</p>
            <video id="video1" controls preload="metadata">
                <source src="http://localhost:3001/api/media/6833c38e73fed52ba6c49982" type="video/mp4">
                Seu navegador não suporta o elemento de vídeo.
            </video>
            <div id="status1" class="log">Aguardando carregamento...</div>
        </div>

        <div class="test-section">
            <h2>Teste 2: Fetch API</h2>
            <p>Testando carregamento via Fetch API:</p>
            <button onclick="testFetch()">Testar Fetch</button>
            <div id="fetchResult" class="log">Clique no botão para testar</div>
        </div>

        <div class="test-section">
            <h2>Teste 3: XMLHttpRequest</h2>
            <p>Testando carregamento via XMLHttpRequest:</p>
            <button onclick="testXHR()">Testar XHR</button>
            <div id="xhrResult" class="log">Clique no botão para testar</div>
        </div>

        <div class="test-section">
            <h2>Teste 4: Vídeo com Blob URL</h2>
            <p>Testando conversão para Blob URL:</p>
            <button onclick="testBlob()">Testar Blob</button>
            <video id="video2" controls preload="metadata" style="display: none;">
                Seu navegador não suporta o elemento de vídeo.
            </video>
            <div id="blobResult" class="log">Clique no botão para testar</div>
        </div>

        <div class="test-section">
            <h2>Logs do Console</h2>
            <div id="consoleLogs" class="log">Logs aparecerão aqui...</div>
        </div>
    </div>

    <script>
        // Capturar logs do console
        const originalLog = console.log;
        const originalError = console.error;
        const consoleDiv = document.getElementById('consoleLogs');
        
        function addLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleDiv.textContent += logEntry;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };

        // Teste 1: Monitorar vídeo direto
        const video1 = document.getElementById('video1');
        const status1 = document.getElementById('status1');
        
        video1.addEventListener('loadstart', () => {
            status1.innerHTML = '<span class="success">✅ Iniciando carregamento...</span>';
            console.log('Video1: loadstart');
        });
        
        video1.addEventListener('loadedmetadata', () => {
            status1.innerHTML = '<span class="success">✅ Metadados carregados</span>';
            console.log('Video1: loadedmetadata');
        });
        
        video1.addEventListener('loadeddata', () => {
            status1.innerHTML = '<span class="success">✅ Dados carregados</span>';
            console.log('Video1: loadeddata');
        });
        
        video1.addEventListener('canplay', () => {
            status1.innerHTML = '<span class="success">✅ Pode reproduzir</span>';
            console.log('Video1: canplay');
        });
        
        video1.addEventListener('error', (e) => {
            status1.innerHTML = `<span class="error">❌ Erro: ${e.target.error?.message || 'Erro desconhecido'}</span>`;
            console.error('Video1: error', e.target.error);
        });

        // Teste 2: Fetch API
        async function testFetch() {
            const resultDiv = document.getElementById('fetchResult');
            resultDiv.textContent = 'Testando...';
            
            try {
                console.log('Iniciando teste Fetch...');
                const response = await fetch('http://localhost:3001/api/media/6833c38e73fed52ba6c49982');
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const contentLength = response.headers.get('content-length');
                const contentType = response.headers.get('content-type');
                
                resultDiv.innerHTML = `
                    <span class="success">✅ Fetch bem-sucedido</span><br>
                    Status: ${response.status}<br>
                    Content-Type: ${contentType}<br>
                    Content-Length: ${contentLength || 'Não especificado'}<br>
                    URL: ${response.url}
                `;
                
                console.log('Fetch bem-sucedido');
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ Erro no Fetch: ${error.message}</span>`;
                console.error('Erro no Fetch:', error);
            }
        }

        // Teste 3: XMLHttpRequest
        function testXHR() {
            const resultDiv = document.getElementById('xhrResult');
            resultDiv.textContent = 'Testando...';
            
            const xhr = new XMLHttpRequest();
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === XMLHttpRequest.DONE) {
                    if (xhr.status === 200) {
                        const contentLength = xhr.getResponseHeader('content-length');
                        const contentType = xhr.getResponseHeader('content-type');
                        
                        resultDiv.innerHTML = `
                            <span class="success">✅ XHR bem-sucedido</span><br>
                            Status: ${xhr.status}<br>
                            Content-Type: ${contentType}<br>
                            Content-Length: ${contentLength || 'Não especificado'}<br>
                            Response size: ${xhr.response?.byteLength || 'Não especificado'} bytes
                        `;
                        
                        console.log('XHR bem-sucedido');
                    } else {
                        resultDiv.innerHTML = `<span class="error">❌ Erro no XHR: ${xhr.status} ${xhr.statusText}</span>`;
                        console.error('Erro no XHR:', xhr.status, xhr.statusText);
                    }
                }
            };
            
            xhr.onerror = function() {
                resultDiv.innerHTML = `<span class="error">❌ Erro de rede no XHR</span>`;
                console.error('Erro de rede no XHR');
            };
            
            console.log('Iniciando teste XHR...');
            xhr.open('GET', 'http://localhost:3001/api/media/6833c38e73fed52ba6c49982');
            xhr.responseType = 'arraybuffer';
            xhr.send();
        }

        // Teste 4: Blob URL
        async function testBlob() {
            const resultDiv = document.getElementById('blobResult');
            const video2 = document.getElementById('video2');
            
            resultDiv.textContent = 'Testando...';
            
            try {
                console.log('Iniciando teste Blob...');
                const response = await fetch('http://localhost:3001/api/media/6833c38e73fed52ba6c49982');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const blob = await response.blob();
                const blobUrl = URL.createObjectURL(blob);
                
                video2.src = blobUrl;
                video2.style.display = 'block';
                
                resultDiv.innerHTML = `
                    <span class="success">✅ Blob criado com sucesso</span><br>
                    Blob size: ${blob.size} bytes<br>
                    Blob type: ${blob.type}<br>
                    Blob URL: ${blobUrl}
                `;
                
                console.log('Blob criado com sucesso');
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ Erro no Blob: ${error.message}</span>`;
                console.error('Erro no Blob:', error);
            }
        }

        // Log inicial
        console.log('Página de teste carregada');
        console.log('Testando URL:', 'http://localhost:3001/api/media/6833c38e73fed52ba6c49982');
    </script>
</body>
</html>
