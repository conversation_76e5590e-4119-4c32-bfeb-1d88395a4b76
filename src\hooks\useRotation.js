import { useState, useEffect, useCallback } from 'react';
import { getRotationCSS, getOrientationCSS, isValidRotation } from '../constants/rotation';

export const useRotation = (initialRotation = 0, initialOrientation = 'auto') => {
  const [rotation, setRotation] = useState(initialRotation);
  const [orientation, setOrientation] = useState(initialOrientation);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Validar e definir rotação
  const updateRotation = useCallback((newRotation) => {
    if (isValidRotation(newRotation)) {
      setIsTransitioning(true);
      setRotation(newRotation);
      
      // Reset transition state after animation
      setTimeout(() => {
        setIsTransitioning(false);
      }, 500);
    }
  }, []);

  // Definir orientação
  const updateOrientation = useCallback((newOrientation) => {
    setOrientation(newOrientation);
  }, []);

  // Obter CSS para aplicar rotação
  const getRotationStyles = useCallback(() => {
    return getOrientationCSS(orientation, rotation);
  }, [rotation, orientation]);

  // Rotacionar para próxima posição (90° horário)
  const rotateClockwise = useCallback(() => {
    const newRotation = (rotation + 90) % 360;
    updateRotation(newRotation);
  }, [rotation, updateRotation]);

  // Rotacionar para posição anterior (90° anti-horário)
  const rotateCounterClockwise = useCallback(() => {
    const newRotation = rotation - 90 < 0 ? 270 : rotation - 90;
    updateRotation(newRotation);
  }, [rotation, updateRotation]);

  // Reset para posição inicial
  const resetRotation = useCallback(() => {
    updateRotation(0);
  }, [updateRotation]);

  // Detectar orientação automática baseada nas dimensões da tela
  const detectOrientation = useCallback(() => {
    if (orientation === 'auto') {
      const isLandscape = window.innerWidth > window.innerHeight;
      return isLandscape ? 'horizontal' : 'vertical';
    }
    return orientation;
  }, [orientation]);

  // Listener para mudanças de orientação da tela
  useEffect(() => {
    const handleOrientationChange = () => {
      if (orientation === 'auto') {
        // Força re-render quando orientação muda
        setRotation(prev => prev);
      }
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
    };
  }, [orientation]);

  return {
    rotation,
    orientation,
    isTransitioning,
    updateRotation,
    updateOrientation,
    getRotationStyles,
    rotateClockwise,
    rotateCounterClockwise,
    resetRotation,
    detectOrientation
  };
};

// Hook para gerenciar rotação de mídia individual
export const useMediaRotation = (mediaConfig = {}) => {
  const {
    rotation: initialRotation = 0,
    orientation: initialOrientation = 'auto',
    fitMode = 'cover',
    duration = 10
  } = mediaConfig;

  const rotationHook = useRotation(initialRotation, initialOrientation);

  const [config, setConfig] = useState({
    rotation: initialRotation,
    orientation: initialOrientation,
    fitMode,
    duration
  });

  // Atualizar configuração
  const updateConfig = useCallback((newConfig) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
    
    if (newConfig.rotation !== undefined) {
      rotationHook.updateRotation(newConfig.rotation);
    }
    
    if (newConfig.orientation !== undefined) {
      rotationHook.updateOrientation(newConfig.orientation);
    }
  }, [rotationHook]);

  // Obter estilos CSS para a mídia
  const getMediaStyles = useCallback(() => {
    const rotationStyles = rotationHook.getRotationStyles();
    
    return {
      ...rotationStyles,
      objectFit: config.fitMode,
      width: '100%',
      height: '100%',
      transition: 'transform 0.5s ease-in-out, object-fit 0.3s ease'
    };
  }, [rotationHook, config.fitMode]);

  return {
    ...rotationHook,
    config,
    updateConfig,
    getMediaStyles
  };
};

// Hook para gerenciar rotação de playlist
export const usePlaylistRotation = (playlistConfig = {}) => {
  const {
    orientation = 'mixed',
    autoRotate = false,
    transitionDuration = 500,
    adaptToContent = true,
    globalRotation = 0
  } = playlistConfig;

  const [config, setConfig] = useState({
    orientation,
    autoRotate,
    transitionDuration,
    adaptToContent,
    globalRotation
  });

  const rotationHook = useRotation(globalRotation, orientation);

  // Atualizar configuração da playlist
  const updatePlaylistConfig = useCallback((newConfig) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
    
    if (newConfig.globalRotation !== undefined) {
      rotationHook.updateRotation(newConfig.globalRotation);
    }
    
    if (newConfig.orientation !== undefined) {
      rotationHook.updateOrientation(newConfig.orientation);
    }
  }, [rotationHook]);

  // Determinar rotação para uma mídia específica
  const getMediaRotation = useCallback((media) => {
    if (!config.adaptToContent) {
      return config.globalRotation;
    }

    // Lógica para adaptar rotação baseada no conteúdo
    if (media.type && media.type.startsWith('video/')) {
      // Para vídeos, manter rotação global ou detectar orientação
      return config.globalRotation;
    }

    // Para imagens, pode adaptar baseado nas dimensões
    return media.rotation || config.globalRotation;
  }, [config]);

  // Obter estilos CSS para o container da playlist
  const getPlaylistStyles = useCallback(() => {
    return {
      ...rotationHook.getRotationStyles(),
      transition: `transform ${config.transitionDuration}ms ease-in-out`
    };
  }, [rotationHook, config.transitionDuration]);

  return {
    ...rotationHook,
    config,
    updatePlaylistConfig,
    getMediaRotation,
    getPlaylistStyles
  };
};
