import { MongoClient, GridFSBucket } from 'mongodb';

const mongoUri = 'mongodb://localhost:27017';
const dbName = 'displaydb';

async function cleanupDatabase() {
  let client;
  
  try {
    console.log('🧹 Iniciando limpeza do banco de dados...');
    
    // Conectar ao MongoDB
    client = await MongoClient.connect(mongoUri);
    const db = client.db(dbName);
    const bucket = new GridFSBucket(db);
    
    console.log('✅ Conectado ao MongoDB');
    
    // 1. Listar todas as mídias na coleção
    const midias = await db.collection('midias').find().toArray();
    console.log(`📊 Encontradas ${midias.length} mídias na coleção`);
    
    // 2. Listar todos os arquivos no GridFS
    const gridFiles = await bucket.find().toArray();
    console.log(`📊 Encontrados ${gridFiles.length} arquivos no GridFS`);
    
    // 3. Encontrar arquivos órfãos no GridFS (sem referência na coleção midias)
    const orphanedFiles = [];
    for (const file of gridFiles) {
      const hasReference = midias.some(midia => 
        midia.fileId?.toString() === file._id.toString() || 
        midia._id?.toString() === file._id.toString()
      );
      
      if (!hasReference) {
        orphanedFiles.push(file);
      }
    }
    
    console.log(`🗑️  Encontrados ${orphanedFiles.length} arquivos órfãos no GridFS`);
    
    // 4. Encontrar mídias órfãs (sem arquivo no GridFS)
    const orphanedMedias = [];
    for (const midia of midias) {
      const fileId = midia.fileId || midia._id;
      const hasFile = gridFiles.some(file => file._id.toString() === fileId.toString());
      
      if (!hasFile) {
        orphanedMedias.push(midia);
      }
    }
    
    console.log(`🗑️  Encontradas ${orphanedMedias.length} mídias órfãs na coleção`);
    
    // 5. Remover arquivos órfãos do GridFS
    if (orphanedFiles.length > 0) {
      console.log('🧹 Removendo arquivos órfãos do GridFS...');
      for (const file of orphanedFiles) {
        try {
          await bucket.delete(file._id);
          console.log(`   ✅ Removido: ${file.filename} (${file._id})`);
        } catch (error) {
          console.log(`   ❌ Erro ao remover ${file.filename}: ${error.message}`);
        }
      }
    }
    
    // 6. Remover mídias órfãs da coleção
    if (orphanedMedias.length > 0) {
      console.log('🧹 Removendo mídias órfãs da coleção...');
      for (const midia of orphanedMedias) {
        try {
          await db.collection('midias').deleteOne({ _id: midia._id });
          console.log(`   ✅ Removido: ${midia.nome} (${midia._id})`);
        } catch (error) {
          console.log(`   ❌ Erro ao remover ${midia.nome}: ${error.message}`);
        }
      }
    }
    
    // 7. Estatísticas finais
    const finalMidias = await db.collection('midias').find().toArray();
    const finalGridFiles = await bucket.find().toArray();
    
    console.log('\n📊 Estatísticas finais:');
    console.log(`   Mídias na coleção: ${finalMidias.length}`);
    console.log(`   Arquivos no GridFS: ${finalGridFiles.length}`);
    console.log(`   Arquivos órfãos removidos: ${orphanedFiles.length}`);
    console.log(`   Mídias órfãs removidas: ${orphanedMedias.length}`);
    
    console.log('\n✅ Limpeza concluída com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro durante a limpeza:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 Conexão com MongoDB fechada');
    }
  }
}

// Executar limpeza
cleanupDatabase();
