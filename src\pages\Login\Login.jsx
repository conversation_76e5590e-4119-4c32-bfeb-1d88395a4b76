import React, { useState } from 'react';
import { <PERSON>, Container, TextField, Button, Typo<PERSON>, Link, Alert } from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { clearGlobalData, forceMigrateAllData } from '../../utils/userDataUtils';
import { useNavigate } from 'react-router-dom';

const validationSchema = yup.object({
  email: yup
    .string()
    .email('Digite um e-mail válido')
    .required('E-mail é obrigatório'),
  password: yup
    .string()
    .min(6, 'A senha deve ter no mínimo 6 caracteres')
    .required('Senha é obrigatória'),
});

const Login = () => {
  const navigate = useNavigate();
  const [status, setStatus] = useState({ type: '', message: '' });

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        // Carregar usuários do localStorage
        const savedUsers = localStorage.getItem('users');
        let users = [];

        if (savedUsers) {
          users = JSON.parse(savedUsers);
        } else {
          // Usuários padrão se não houver nenhum salvo
          users = [
            {
              id: 1,
              name: 'Sandro Denis',
              email: '<EMAIL>',
              password: 'Sandro2010',
              isAdmin: true,
              createdAt: new Date().toISOString()
            },
            {
              id: 2,
              name: 'Usuário Teste',
              email: '<EMAIL>',
              password: '123456',
              isAdmin: false,
              createdAt: new Date().toISOString()
            }
          ];
          localStorage.setItem('users', JSON.stringify(users));
        }

        // Verificar credenciais
        const user = users.find(u => u.email === values.email && u.password === values.password);

        if (user) {
          localStorage.setItem('token', user.isAdmin ? 'admin-token-123' : 'user-token-123');
          localStorage.setItem('userEmail', user.email);
          localStorage.setItem('userName', user.name);
          localStorage.setItem('isAdmin', user.isAdmin.toString());

          // Limpar dados globais antigos e migrar para o usuário
          console.log(`🔐 Login realizado para: ${user.name} (${user.email})`);
          clearGlobalData();
          forceMigrateAllData();

          navigate('/dashboard');
        } else {
          setStatus({
            type: 'error',
            message: 'E-mail ou senha inválidos',
          });
        }
      } catch (error) {
        setStatus({
          type: 'error',
          message: 'Erro ao fazer login. Tente novamente.',
        });
      }
    },
  });

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          bgcolor: 'white',
          p: 4,
          borderRadius: 2,
          boxShadow: '0 3px 5px 2px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Typography component="h1" variant="h5" sx={{ mb: 3, color: '#333' }}>
          Login
        </Typography>

        {status.type && (
          <Alert severity={status.type} sx={{ width: '100%', mb: 2 }}>
            {status.message}
          </Alert>
        )}
        <Box component="form" onSubmit={formik.handleSubmit} sx={{ mt: 1, width: '100%' }}>
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="E-mail"
            name="email"
            autoComplete="email"
            autoFocus
            value={formik.values.email}
            onChange={formik.handleChange}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="normal"
            required
            fullWidth
            name="password"
            label="Senha"
            type="password"
            id="password"
            autoComplete="current-password"
            value={formik.values.password}
            onChange={formik.handleChange}
            error={formik.touched.password && Boolean(formik.errors.password)}
            helperText={formik.touched.password && formik.errors.password}
            sx={{ mb: 3 }}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{
              mt: 3,
              mb: 2,
              bgcolor: '#007BFF',
              '&:hover': {
                bgcolor: '#0056b3',
              },
              height: '48px',
              fontSize: '1rem',
            }}
          >
            Entrar
          </Button>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Link href="/recuperar-senha" variant="body2" sx={{ color: '#007BFF' }}>
              Esqueceu a senha?
            </Link>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};

export default Login;